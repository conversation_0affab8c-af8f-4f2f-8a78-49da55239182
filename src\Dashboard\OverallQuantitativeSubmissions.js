import React, { useState, useEffect, useRef } from "react";
import { API } from "../components/constants/api_url";
import APIServices from "../service/APIService";
import { hardcoded } from "../pages/constants/hardcodedid";
import { Dropdown } from "primereact/dropdown";
import { Dialog } from "primereact/dialog";
import { motion } from "framer-motion";
import { useSelector } from "react-redux";
import useForceUpdate from "use-force-update";
import { useHistory } from "react-router-dom";
import { TabMenu } from 'primereact/tabmenu';
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column"
import { Tooltip } from "primereact/tooltip";
import Swal from 'sweetalert2'
import { Tag } from "primereact/tag";
import { Button } from "primereact/button";
import { OverlayPanel } from 'primereact/overlaypanel';
import { MultiSelect } from "primereact/multiselect";
import { BlockUI } from 'primereact/blockui';
import { DateTime } from "luxon";
import { Paginator } from "primereact/paginator";

const OverallQuantitativeSubmissions = () => {

    const dpnamerefs = [useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null), useRef(null)]
    const formref = useRef(null)
    const [first, setFirst] = useState(0);
    const [rows, setRows] = useState(20);
    const entityref = useRef(null)
    const userList = useSelector(state => state.userlist.userList)
    const [tempload, setTempLoad] = useState(false)
    const [assignedlist, setAssignedList] = useState([])
    const [statusCodes, setStatusCodes] = useState({ '0': 0, '1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0, '7': 0, '8': 0 })
    const [quantitativesubmission, setQuantiativeSubmission] = useState([])
    const [stdlist, setStdList] = useState([])
    const [subcat1, setSubCat1] = useState([])
    const [subcat2, setSubCat2] = useState([])
    const [subcat3, setSubCat3] = useState([])
    const [subcat4, setSubCat4] = useState([])
    const statusref = useRef(null)
    const login_data = useSelector((state) => state.user.userdetail);
    const [entityoption, setEntityOption] = useState([])
    const [formoption, setFormOption] = useState([])
    const [yearoption, setYearOption] = useState([])
    const [statusoption, setStatusOption] = useState([])
    const navigate = useHistory();
    const [submittedQuantitative, setSubmittedQuantitative] = useState([]);
    const [overallsubmittedQuantitative, setOverallSubmittedQuantitative] =
        useState([]);
    const [rawsitelist, setRawSitelist] = useState([]);
    const [filter, setFilter] = useState({ year: null, entity: [], form: [], status: [] });
    const [refineddata, setRefinedData] = useState([]);
    const [refineddatabk, setRefinedDataBk] = useState([]);
    const [dcflist, setDcfList] = useState([]);
    const [dplist, setDPList] = useState([]);
    const [load, setLoading] = useState(false);
    const forceUpdate = useForceUpdate();
    const [remarksdialog, setRemarksDialog] = useState(false)
    const [remarksdata, setRemarksData] = useState([])
    const frequency_list = [
        { name: "Monthly", id: 1 },
        { name: "Bi-Monthly", id: 2 },
        { name: "Quartely", id: 3 },
        { name: "Annually", id: 4 },
        { name: "Bi-Annually", id: 5 },
        { name: "Undefined", id: 6 },
    ];



    useEffect(() => {

        let uriString = {
            include: [
                {
                    relation: "locationTwos",
                    scope: { include: [{ relation: "locationThrees" }] },
                },
            ],
        };
        let efstd = {
            include: [
                {
                    relation: "newEfCategories",
                    scope: {
                        include: [
                            {
                                relation: "newEfSubcategory1s",
                                scope: {
                                    include: [
                                        {
                                            relation: "newEfSubcategory2s",
                                            scope: {
                                                include: [
                                                    {
                                                        relation: "newEfSubcategory3s",
                                                        scope: {
                                                            include: [{ relation: "newEfSubcategory4s" }],
                                                        },
                                                    },
                                                ],
                                            },
                                        },
                                    ],
                                },
                            },
                        ],
                    },
                },
            ],
        };
        let uriString2 = {
            include: [
                {
                    relation: "newTopics",
                    scope: {
                        include: [
                            {
                                relation: "newMetrics",
                                scope: { include: [{ relation: "newDataPoints" }] },
                            },
                        ],
                    },
                },
            ],
        };
        let Overall = API.Categories + `?filter=${encodeURIComponent(JSON.stringify(uriString2))}`;
        const promise0 = APIServices.get(API.DCF);
        const promise1 = APIServices.get(
            API.QN_Submit_UP(login_data.information.cid)
        )
        const promise2 = APIServices.get(
            API.LocationOne_UP(login_data.information.cid) +
            `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
        );
        const promise3 = APIServices.get(
            API.DCF_Entity_UP(login_data.information.cid)
        );
        const promise4 = APIServices.get(
            API.DF_Entity_UP(login_data.information.cid)
        );
        const promise5 = APIServices.get(
            API.SRF_Entity_UP(login_data.information.cid)
        );
        const promise6 = APIServices.get(
            API.DCF_Entity_User_UP(login_data.information.cid)
        );
        const promise7 = APIServices.get(
            API.DF_Entity_User_UP(login_data.information.cid)
        );
        const promise8 = APIServices.get(
            API.SRF_Entity_User_UP(login_data.information.cid)
        );
        const promise9 = APIServices.get(API.DataPoint);
        const promise10 = APIServices.get(API.EF_Std + `?filter=${encodeURIComponent(JSON.stringify(efstd))}`)
        const promise11 = APIServices.get(API.EF_SC1);
        const promise12 = APIServices.get(API.EF_SC2);
        const promise13 = APIServices.get(API.EF_SC3);
        const promise14 = APIServices.get(API.EF_SC4);
        const promise15 = APIServices.get(
            API.AssignDCFClient_UP(login_data.information.cid)
        );
        const promise16 = APIServices.get(
            Overall
        );
        Promise.all([
            promise0,
            promise1,
            promise2,
            promise3,
            promise4,
            promise5,
            promise6,
            promise7,
            promise8,
            promise9, promise10, promise11, promise12, promise13, promise14, promise15, promise16
        ]).then((values) => {
            let curated_dcf_ids = []
            if (values[15].data && values[15].data.length > 0) {
                values[16].data.forEach((cat) => {
                    if (cat.newTopics !== undefined) {

                        cat.newTopics.forEach((topic) => {

                            if (topic.newMetrics !== undefined && (topic.tag === null || parseFloat(topic.tag) === login_data.information.cid)) {

                                topic.newMetrics.forEach((metric) => {
                                    if (Array.isArray(metric.data1) && metric.data1[0].type !== undefined && metric.data1[0].type === 0 && (metric.tag === null || metric.tag === login_data.information.cid)) {
                                        if (metric.newDataPoints !== undefined) {
                                            metric.newDataPoints.forEach((dp) => {
                                                if (Array.isArray(dp.data1) && dp.data1.length !== 0 && dp.data1[0].datasource !== null && typeof dp.data1[0].datasource === 'number') {
                                                    let dcf_index = values[0].data.findIndex(i => i.id === dp.data1[0].datasource)
                                                    if (!curated_dcf_ids.includes(dp.data1[0].datasource) && dcf_index !== -1) {

                                                        if (values[0].data[dcf_index].tags === null || !values[0].data[dcf_index].tags.length || values[0].data[dcf_index].tags.includes(login_data.information.cid)) {
                                                            curated_dcf_ids.push(dp.data1[0].datasource)
                                                        }

                                                    }
                                                }
                                            })
                                        }
                                    }
                                })
                            }
                        })
                    }


                })
            }
            setSubCat1(values[11].data)
            setSubCat2(values[12].data)
            setSubCat3(values[13].data)
            setSubCat4(values[14].data)

            let stdList = values[10].data
            let quantitative_submitted = values[1].data,
                qn_submit_list = [],
                overall_qn_submit_list = [],
                dcf_list = values[0].data,
                quantitative_ass_list = values[6].data,
                qualitative_ass_list = values[7].data,
                srf_ass_list = values[8].data;
            let quantitative_entity_list = values[3].data,
                qualitative_entity_list = values[4].data,
                srf_entity_list = values[5].data;
            const shapedSite = values[2].data
                .map((item) => {
                    if (item.locationTwos) {
                        item.locationTwos = item.locationTwos.filter(
                            (locationTwo) =>
                                locationTwo.locationThrees &&
                                locationTwo.locationThrees.length > 0
                        );
                    }
                    return item;
                })
                .filter((item) => item.locationTwos && item.locationTwos.length > 0);
            let filtered_qn_ass = quantitative_ass_list.filter(
                (i) =>
                    dcf_list.map((j) => j.id).includes(i.dcfId) && curated_dcf_ids.includes(i.dcfId) &&
                    checkEnity(i, quantitative_entity_list, shapedSite, "dcfId")
            );
            console.log(
                filtered_qn_ass.filter((i) => i.reporter_ids.includes(login_data.id))
            );

            setDcfList(dcf_list);
            setDPList(values[9].data);
            setRawSitelist(shapedSite);
            setStdList(values[10].data)
            quantitative_submitted.forEach((item) => {
                let filtered_qn_ass_index = filtered_qn_ass.findIndex((i) => {
                    console.log(
                        i.id,
                        item.entityUserAssId,
                        i.entityAssId,
                        item.entityAssId
                    );
                    return (
                        i.id === item.entityUserAssId && i.entityAssId === item.entityAssId
                    );
                });
                if (filtered_qn_ass_index !== -1) {
                    overall_qn_submit_list.push(item);
                    qn_submit_list.push(item);
                }

            });
            setAssignedList(filtered_qn_ass)
            setQuantiativeSubmission(quantitative_submitted)

            calculateAndCreateYearData(
                filtered_qn_ass,
                values[0].data,
                quantitative_submitted, stdList, { cat1: values[11].data, cat2: values[12].data, cat3: values[13].data, cat4: values[14].data }, DateTime.utc().toLocal().year
            ).then((res) => {
                
                let entity_option = Array.from(new Set(res.map(i => getCoverageTextById(i, shapedSite)))).map(i => ({ title: i, id: i }))
                setFormOption(dcf_list.filter(i => res.map(i => i.dcfId).includes(i.id)))
                setEntityOption(entity_option)
                let year_opt = Array.from(new Set(res.map(i => i.year))).map(i => ({ value: i, title: i }))
                if (year_opt.findIndex(i => i.value === DateTime.utc().toLocal().year) === -1) {
                    year_opt.push({ value: DateTime.utc().toLocal().year, title: DateTime.utc().toLocal().year })
                }
                setYearOption(year_opt)
                if (res.length) {

                    onSelectedChangeIntial('year', DateTime.utc().toLocal().year, res)
                } else {
                    setLoading(true);
                }
                // setRefinedData(res);
                setRefinedDataBk(res);

            });
            setOverallSubmittedQuantitative(
                overall_qn_submit_list.filter((i) => i.type !== 0)
            );
            setSubmittedQuantitative(qn_submit_list.filter((i) => i.type === 1));
            extractDPFromForms(qn_submit_list.filter((i) => i.type === 1));

        });
    }, []);


    function getMonthsBetween_(start_date, end_date, frequency, year) {
        const startDate = DateTime.fromISO(start_date);
        const endDate = end_date ? DateTime.fromISO(end_date) : DateTime.utc();

        const months = [];
        let currentMonth = startDate;

        while (currentMonth <= endDate) {
            console.log(currentMonth.year == year, currentMonth.year)
            if (frequency === 1) {
                console.log(currentMonth.toLocal().startOf('month').diff(DateTime.utc().toLocal(), 'days').days)
                if (currentMonth.toLocal().startOf('month').diff(DateTime.utc().toLocal(), 'days').days <= 0 && currentMonth.year === year) {
                    months.push(currentMonth.toFormat("LLL-yyyy"));
                }
            } else {

                if (currentMonth.toLocal().startOf('month').diff(DateTime.utc().toLocal(), 'days').days <= 0 && currentMonth.year === year) {
                    console.log(currentMonth.plus({ months: frequency - 1 }).toFormat("LLL-yyyy"), currentMonth.plus({ months: frequency - 1 }).toLocal().startOf('month').diff(endDate.toLocal(), 'days').days)
                    months.push(
                        currentMonth.toFormat("LLL-yyyy") +
                        " to " +
                        currentMonth.plus({ months: frequency - 1 }).toFormat("LLL-yyyy")
                    );
                }


            }

            currentMonth = currentMonth.plus({ months: frequency });
        }

        return months;
    }

    function getMonthsBetween(start_date, end_date, frequency, filterYear) {
        const startDate = DateTime.fromISO(start_date, { zone: 'utc' }).plus({ months: 1 });
        const endDate = end_date ? DateTime.fromISO(end_date, { zone: 'utc' }).plus({ months: 1 }) : DateTime.utc().plus({ months: 1 });

        const months = [];
        let currentMonth = startDate;
        console.log(currentMonth.year, filterYear)
        while (currentMonth <= endDate) {
            if (currentMonth.year === filterYear) {
                if (frequency === 1) {
                    console.log(currentMonth.startOf('month').diff(DateTime.utc(), 'months').months, currentMonth.toFormat("LLL-yyyy"))
                    if (currentMonth.startOf('month').diff(DateTime.utc(), 'months').months <= -1) {
                        months.push(currentMonth.toFormat("LLL-yyyy"));
                    }
                } else {

                    if (currentMonth.startOf('month').diff(DateTime.utc(), 'months').months <= -1) {
                        console.log(currentMonth.plus({ months: frequency - 1 }).toFormat("LLL-yyyy"), currentMonth.plus({ months: frequency - 1 }).toLocal().startOf('month').diff(endDate.toLocal(), 'days').days)
                        months.push(
                            currentMonth.toFormat("LLL-yyyy") +
                            " to " +
                            currentMonth.plus({ months: frequency - 1 }).toFormat("LLL-yyyy")
                        );
                    }


                }
            }
            currentMonth = currentMonth.plus({ months: frequency });
        }

        return months;
    }
    const getAllStatusCode = (arr) => {
        console.log(arr)
        return new Promise((resolve, err) => {
            let codes = []
            arr.forEach((i, inx) => {
                Object.entries(i).forEach((j) => {
                    if (!j[0].includes('name')) {
                        console.log(j)
                        if (j[1].length) {
                            if (j[1][0].id !== undefined) {
                                if (j[1][0].status === 5) {


                                    codes.push(7)


                                } else if (j[1][0].status === 4) {


                                    codes.push(6)


                                } else if (j[1][0].status === 3) {


                                    codes.push(5)


                                } else if (j[1][0].status === 2) {


                                    codes.push(8)


                                }
                                else if (j[1][0].status === 1) {


                                    codes.push(1)


                                } else if (getOverdueDays(j[0]) <= -15 && (j[1][0].status === null || j[1][0].status === 0)) {


                                    codes.push(2)


                                } else if (getOverdueDays(j[0]) <= 0 && (j[1][0].status === null || j[1][0].status === 0)) {


                                    codes.push(3)


                                } else if (getOverdueDays(j[0]) > 0 && (j[1][0].status === null || j[1][0].status === 0)) {


                                    codes.push(4)


                                }
                            } else {

                                if (getOverdueDays(j[0]) <= -15) {


                                    codes.push(2)


                                } else if (getOverdueDays(j[0]) <= 0) {


                                    codes.push(3)


                                } else if (getOverdueDays(j[0]) > 0) {


                                    codes.push(4)


                                }
                            }
                        } else {
                            console.log(j, i)
                            if (getOverdueDays(j[0]) <= -15) {



                                codes.push(2)


                            } else if (getOverdueDays(j[0]) <= 0) {


                                codes.push(3)


                            } else if (getOverdueDays(j[0]) > 0) {


                                codes.push(4)


                            }

                        }
                    }
                })
            })

            resolve(codes)
        })



    }
    const getStatusCode = (arr) => {
        console.log(arr)
        return new Promise((resolve, err) => {
            let codes = []
            arr.forEach((i, inx) => {
                Object.entries(i).forEach((j) => {
                    if (!j[0].includes('name')) {
                        console.log(j)
                        if (j[1].length) {
                            if (j[1][0].id !== undefined) {
                                if (j[1][0].status === 5) {

                                    if (!codes.includes(7)) {
                                        codes.push(7)

                                    }
                                } else if (j[1][0].status === 4) {

                                    if (!codes.includes(6)) {
                                        codes.push(6)

                                    }
                                } else if (j[1][0].status === 3) {

                                    if (!codes.includes(5)) {
                                        codes.push(5)

                                    }
                                } else if (j[1][0].status === 2) {

                                    if (!codes.includes(8)) {
                                        codes.push(8)

                                    }
                                }
                                else if (j[1][0].status === 1) {

                                    if (!codes.includes(1)) {
                                        codes.push(1)

                                    }
                                } else if (getOverdueDays(j[0]) <= -15 && (j[1][0].status === null || j[1][0].status === 0)) {

                                    if (!codes.includes(2)) {
                                        codes.push(2)

                                    }
                                } else if (getOverdueDays(j[0]) <= 0 && (j[1][0].status === null || j[1][0].status === 0)) {

                                    if (!codes.includes(3)) {
                                        codes.push(3)

                                    }
                                } else if (getOverdueDays(j[0]) > 0 && (j[1][0].status === null || j[1][0].status === 0)) {

                                    if (!codes.includes(4)) {
                                        codes.push(4)

                                    }
                                }
                            } else {

                                if (getOverdueDays(j[0]) <= -15) {

                                    if (!codes.includes(2)) {
                                        codes.push(2)

                                    }
                                } else if (getOverdueDays(j[0]) <= 0) {

                                    if (!codes.includes(3)) {
                                        codes.push(3)

                                    }
                                } else if (getOverdueDays(j[0]) > 0) {

                                    if (!codes.includes(4)) {
                                        codes.push(4)

                                    }
                                }
                            }
                        } else {
                            console.log(j, i)
                            if (getOverdueDays(j[0]) <= -15) {


                                if (!codes.includes(2)) {
                                    codes.push(2)

                                }
                            } else if (getOverdueDays(j[0]) <= 0) {

                                if (!codes.includes(3)) {
                                    codes.push(3)

                                }
                            } else if (getOverdueDays(j[0]) > 0) {

                                if (!codes.includes(4)) {
                                    codes.push(4)

                                }
                            }

                        }
                    }
                })
            })
            if (!codes.length) {
                codes.push(0)
            }
            resolve(codes)
        })



    }
    const getDPNameHC = (arr) => {
        let result = [], count = 0
        console.log(arr)
        arr.forEach((i) => {
            Object.values(i).forEach((j) => {
                j.forEach((k) => {
                    if (result.findIndex(x => x.label === k.dp) === -1) {
                        count++
                        result.push({ label: k.dp, order: count })
                    }
                })

            })
        })
        return result
    }
    async function calculateAndCreateYearData(
        assignment,
        dcf_list,
        submission_list, efstdlist, subcat, requiredyear
    ) {
        const yearData = [];
        const uniqueCombinations = [];
        const actualData = [];
        return new Promise((resolve, reject) => {
            assignment.forEach((item) => {
                console.log(item)
                // Extract necessary information
                const {
                    start_date,
                    end_date,
                    tier0_id,
                    tier1_id,
                    tier2_id,
                    tier3_id,
                    dcfId, locationId, entityAssId, frequency, level,
                    standard,
                    dataobject
                } = item;

                // Convert start_date and end_date to Luxon DateTime objects
                const startDate = DateTime.fromISO(start_date);
                const endDate = end_date ? DateTime.fromISO(end_date) : DateTime.utc();

                // Generate year array based on start_date and end_date
                const startDateYear = startDate.year;
                const endDateYear = endDate.year;
                const yearArray = Array.from(
                    { length: endDateYear - startDateYear + 1 },
                    (_, index) => startDateYear + index
                );

                // Check if the combination already exists
                const existingCombinationIndex = uniqueCombinations.findIndex(
                    (combination) => {
                        return (
                            combination.tier0_id === tier0_id &&
                            combination.tier1_id === tier1_id &&
                            combination.tier2_id === tier2_id &&
                            combination.tier3_id === tier3_id && combination.standard === standard &&
                            combination.dcfId === dcfId
                        );
                    }
                );

                if (existingCombinationIndex !== -1) {
                    const existingYears = new Set(
                        uniqueCombinations[existingCombinationIndex].year
                    );

                    // Add new years from yearArray to the existingYears Set
                    yearArray.forEach((year) => {
                        existingYears.add(year);
                    });
                    actualData.push({
                        ...item,
                        year: yearArray,
                        dataobject: getMonthsBetween(
                            start_date,
                            end_date,
                            frequency === 4 ? 12 : frequency === 5 ? 6 : frequency,requiredyear
                        )
                    })
                    uniqueCombinations[existingCombinationIndex].year =
                        Array.from(existingYears);
                    // Convert the Set back to an array and update the uniqueCombinations entry
                    uniqueCombinations[existingCombinationIndex].dataobject = [
                        ...uniqueCombinations[existingCombinationIndex].dataobject,
                        ...getMonthsBetween(
                            start_date,
                            end_date,
                            frequency === 4 ? 12 : frequency === 5 ? 6 : frequency, requiredyear
                        ),
                    ];
                } else {
                    // If the combination doesn't exist, create a new object
                    uniqueCombinations.push({
                        ...item,
                        year: yearArray,
                        dataobject: getMonthsBetween(
                            start_date,
                            end_date,
                            frequency === 4 ? 12 : frequency === 5 ? 6 : frequency, requiredyear
                        ),
                    });
                    actualData.push({
                        ...item,
                        year: yearArray,
                        dataobject: getMonthsBetween(
                            start_date,
                            end_date,
                            frequency === 4 ? 12 : frequency === 5 ? 6 : frequency,requiredyear
                        )
                    })
                }
            });
            uniqueCombinations.forEach((item) => {
                console.log(item)
                const {
                    start_date,
                    end_date,
                    tier0_id,
                    tier1_id,
                    tier2_id,
                    tier3_id,
                    dcfId, locationId, entityAssId, frequency, level,
                    standard,
                    dataobject
                } = item;
                let newObj = { standard, company_id: login_data.clientId, level, locationId, entityAssId, entityUserAssId: item.id, frequency, self: item.reviewer_ids.length !== 0 ? false : true }
                
                const dcfItem = dcf_list.find((item) => item.id === dcfId);

                
                if (dcfItem) {
                    let data1ForYear = [],
                        msn = [
                            {
                                name: JSON.parse(
                                    JSON.stringify(
                                        dcfItem.data1.filter((obj) => obj.type === "number")
                                    )
                                ),
                            },
                        ];
                    data1ForYear = JSON.parse(
                        JSON.stringify(dcfItem.data1.filter((obj) => obj.type === "number"))
                    );
                    item.year.forEach((y) => {
                        console.log(
                            item.dataobject.filter((i) => i.includes(y.toString())).length,
                            y
                        );
                        item.dataobject
                            .filter((i) => i.includes(y.toString()))
                            .forEach((xy, xindx) => {
                                let act = actualData.filter(i => i.dcfId === item.dcfId && i.tier0_id === tier0_id && i.tier1_id === tier1_id && i.tier2_id === tier2_id && i.tier3_id === tier3_id && i.standard === standard && i.dataobject.includes(xy))
                                console.log(actualData,item)
                                let newObj2 = { company_id: login_data.clientId, level, locationId, entityAssId }
                                newObj2['self'] = act[0].reviewer_ids.length === 0 ? true : false
                                newObj2['refobj'] = item
                                newObj2['frequency'] = act[0].frequency
                                newObj2['entityUserAssId'] = act[0].id
                                newObj2['standard'] = act[0].standard
                                if (hardcoded.dcf.includes(dcfId.toString()) || hardcoded.dcf2.includes(dcfId.toString())) {
                                    if (dcfId === 304) {
                                        msn.push(getValueByMonthlyHC3Sub(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            ['DPA0131', 'DPA0132', 'DPA0336'],
                                            submission_list, efstdlist, subcat, standard, '', newObj2.self, newObj2))
                                    } else if (dcfId === 287) {

                                        msn.push(getValueByMonthlyElectricity287(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            ['DPAN095', 'DPAN095A', 'DPAN095B', 'energyval'],
                                            submission_list, efstdlist, subcat, standard, '', newObj2.self, newObj2))
                                    } 
                                    else if (dcfId === 305 || dcfId === 16) {
                                        msn.push(getValueByMonthlyHC1Sub(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            dcfId === 305 ? ['DPA0136', 'DPA0138'] : ['DPA0287', 'DPA0289'],
                                            submission_list, efstdlist, subcat, standard, dcfId === 305 ? 'kg' : 'USD', newObj2.self, newObj2))

                                    } else if (dcfId === 286) {
                                        msn.push(getValueByMonthlyWaterD286(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'm3', newObj2.self, newObj2))

                                    } else if (dcfId === 285) {
                                        msn.push(getValueByMonthlyWaterW285(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'm3', newObj2.self, newObj2))

                                    } else if (dcfId === 300) {
                                        msn.push(getValueByMonthlyBTAir(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'km', newObj2.self, newObj2))

                                    }
                                    else if (dcfId === 302) {
                                        console.log(xy)
                                        msn.push(getValueByMonthlyBTRail(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'km', newObj2.self, newObj2))

                                    }
                                    else if (dcfId === 301) {
                                        console.log(xy)
   msn.push(getValueByMonthlyBTLand(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'km', newObj2.self, newObj2))
                                    }
                                    else if (dcfId === 297) {
                                        console.log(xy)
                                        msn.push(getValueByMonthlyHazWaste297(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'km', newObj2.self, newObj2))

                                    }
                                    else if (dcfId === 307) {
                                        console.log(xy)
                                        msn.push(getValueByMonthlyNonHazWaste307(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'km', newObj2.self, newObj2))

                                    }
                                    else if (dcfId === 275) {
                                        console.log(xy)
                                        msn.push(getValueByMonthlyEmpCat275(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'nos', newObj2.self, newObj2))

                                    } else if (dcfId === 284) {
                                        console.log(xy)
                                        msn.push(getValueByMonthlyEmpNewTurn284(dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            submission_list, 'nos', newObj2.self, newObj2))

                                    }
                                    else {

                                        msn.push(getValueByMonthlyHC(
                                            dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            data1ForYear.map((i) => i.name),
                                            submission_list, newObj2.self, newObj2
                                        ))
                                    }


                                } else {
                                    msn.push(
                                        getValueByMonthly(
                                            dcfId,
                                            tier0_id,
                                            tier1_id,
                                            tier2_id,
                                            tier3_id,
                                            [xy],
                                            data1ForYear.map((i) => i.name),
                                            submission_list, newObj2.self, newObj2
                                        )
                                    );
                                }

                            });
                    });
                    
                    item.year.forEach(async (y) => {
                        if (dcfId === 304 || dcfId === 305 || dcfId === 16 || dcfId === 286 || dcfId === 285 || dcfId === 300 || dcfId === 301 || dcfId === 302 || dcfId === 297 || dcfId === 307 || dcfId === 275 || dcfId === 284 || dcfId === 287) {
                            let msn_ = msn.filter((i) => {
                                return (Object.keys(i)[0].includes(y.toString())
                                );
                            })
                            let names_ = getDPNameHC(msn_)
                            msn_.forEach((i) => {
                                Object.values(i).forEach((j) => {
                                    let oldObj = {}
                                    let oldObjInd = j.findIndex(l => { return (l.id !== null && l.id !== undefined) })
                                    if (oldObjInd !== -1) {
                                        console.log(j[oldObjInd])
                                        oldObj = { id: j[oldObjInd].id, type: j[oldObjInd].type, status: j[oldObjInd].status, remarks: j[oldObjInd].remarks === undefined ? null : j[oldObjInd].remarks }
                                    }
                                    names_.forEach((k) => {
                                        let nameindex = j.findIndex(l => l.dp === k.label)

                                        if (nameindex === -1 && k.label !== null) {
                                            j.push({ ...oldObj, category: null, diff: false, form_type: 2, current: '-', dp: k.label, order: k.order })
                                        } else if (k.label !== null) {
                                            j[nameindex].order = k.order
                                        }
                                    })
                                    j.sort((a, b) => { return a.order - b.order }, 0)
                                })
                            })
                            console.log(msn_, dcfId)
                            msn_.unshift({ name: names_.filter(i => { return i.label !== null }) })
                            yearData.push({
                                ...newObj,
                                year: y,
                                tier0_id,
                                tier1_id,
                                tier2_id,
                                tier3_id,
                                dcfId,
                                form_type: hardcoded.dcf.includes(dcfId.toString()) || hardcoded.dcf2.includes(dcfId.toString()) ? 2 : 1,
                                status: await getStatusCode(msn_.filter((i) => {
                                    return (
                                        Object.keys(i)[0] === "name" ||
                                        Object.keys(i)[0].includes(y.toString())
                                    );
                                })),
                                statusArray: await getAllStatusCode(msn_.filter((i) => {
                                    return (
                                        Object.keys(i)[0] === "name" ||
                                        Object.keys(i)[0].includes(y.toString())
                                    );
                                })),
                                dp_array: msn_.filter((i) => {
                                    return (
                                        Object.keys(i)[0] === "name" ||
                                        Object.keys(i)[0].includes(y.toString())
                                    );
                                }),
                            });
                        } else {
                            console.log(dcfId, msn)
                            yearData.push({
                                ...newObj,
                                year: y,
                                tier0_id,
                                tier1_id,
                                tier2_id,
                                tier3_id,
                                dcfId,
                                form_type: hardcoded.dcf.includes(dcfId.toString()) || hardcoded.dcf2.includes(dcfId.toString()) ? 2 : 1,
                                status: await getStatusCode(msn.filter((i) => {
                                    return (
                                        Object.keys(i)[0] === "name" ||
                                        Object.keys(i)[0].includes(y.toString())
                                    );
                                })),
                                statusArray: await getAllStatusCode(msn.filter((i) => {
                                    return (
                                        Object.keys(i)[0] === "name" ||
                                        Object.keys(i)[0].includes(y.toString())
                                    );
                                })),
                                dp_array: msn.filter((i) => {
                                    return (
                                        Object.keys(i)[0] === "name" ||
                                        Object.keys(i)[0].includes(y.toString())
                                    );
                                }),
                            });
                        }


                    });
                }
                // yearData.forEach((i) => {
                //     i.dp_array.forEach((j,indx) => {
                //         Object.keys(j).forEach(async (k) => {
                //             if (k.includes('-'+i.year)) {

                //                 i.dp_array[indx][k]  =  await getValueByMonthly(i.dcfId,i.tier0_id,i.tier1_id,i.tier2_id,i.tier3_id,k,j.name,submission_list )

                //             }
                //         })
                //     })

                // })
                
            });

            resolve(yearData);
        });
    }

    function getKey(frequency, year) {
        const months = Array.from({ length: 12 }, (_, i) =>
            DateTime.local(year, i + 1, 1).toFormat("LLL")
        );
        const keys = [];
        
        switch (frequency) {
            case 1:
                months.forEach((month) => {
                    const key = month + "-" + year;
                    keys.push({ [key]: {} });
                });
                break;
            case 2:
                for (let i = 0; i < months.length; i += 2) {
                    const startMonthIndex = i;
                    const endMonthIndex = Math.min(
                        startMonthIndex + 1,
                        months.length - 1
                    );
                    const key =
                        months[startMonthIndex] +
                        "-" +
                        year +
                        " to " +
                        months[endMonthIndex] +
                        "-" +
                        year;
                    keys.push({ [key]: {} });
                }
                break;
            case 3:
                for (let i = 0; i < months.length; i += 3) {
                    const startMonthIndex = i;
                    const endMonthIndex = Math.min(
                        startMonthIndex + 2,
                        months.length - 1
                    );
                    const key =
                        months[startMonthIndex] +
                        "-" +
                        year +
                        " to " +
                        months[endMonthIndex] +
                        "-" +
                        year;
                    keys.push({ [key]: {} });
                }
                break;
            case 4:
                keys.push({
                    [`${months[0]}-${year} to ${months[months.length - 1]}-${year}`]: {},
                });
                break;
            case 5:
                const midIndex = Math.ceil(months.length / 2);
                const firstHalf = months.slice(0, midIndex);
                const secondHalf = months.slice(midIndex);
                const key1 =
                    firstHalf[0] +
                    "-" +
                    year +
                    " to " +
                    firstHalf[firstHalf.length - 1] +
                    "-" +
                    year;
                const key2 =
                    secondHalf[0] +
                    "-" +
                    year +
                    " to " +
                    secondHalf[secondHalf.length - 1] +
                    "-" +
                    year;
                keys.push({ [key1]: {} }, { [key2]: {} });
                break;
            default:
                break;
        }

        // Create the final object
        const result = {};
        keys.forEach((keyObj) => {
            Object.assign(result, keyObj);
        });
        
        return result;
    }
    function getPreviousPeriod(frequency, key) {
        if (frequency === 1) {
            const [month, year] = key.split("-");
            const previousDate = DateTime.fromFormat(
                `${month}-1-${year}`,
                "LLL-d-yyyy"
            ).minus({ months: 1 });
            const previousMonth = previousDate.toFormat("LLL");
            const previousYear = previousDate.year;
            return previousMonth + "-" + previousYear;
        } else {
            const endMonth = key.split(" to ")[0].split("-")[0].trim();
            const endYear = key.split(" to ")[0].split("-")[1].trim();
            const previousDate = DateTime.fromFormat(
                `${endMonth}-1-${endYear}`,
                "LLL-d-yyyy"
            ).minus({ months: 1 });
            const previousMonth = previousDate.toFormat("LLL");
            const previousYear = previousDate.year;
            return previousMonth + "-" + previousYear;
        }
    }
    const getRPTextFormat = (item) => {
        if (item.length !== 0) {
            if (item.length >= 2) {
                const startDate = DateTime.fromFormat(item[0], "MM-yyyy").toFormat(
                    "LLL-yyyy"
                );
                const endDate = DateTime.fromFormat(
                    item[item.length - 1],
                    "MM-yyyy"
                ).toFormat("LLL-yyyy");
                return `${startDate} to ${endDate}`;
            } else {
                return DateTime.fromFormat(item[0], "MM-yyyy").toFormat("LLL-yyyy");
            }
        }
    };
    function calculatePercentage(value1, value2) {
        if ((value1 === null || value1 === 0) && (value2 === null || value2 === 0)) {
            return '-'
        }
        if (value2 === 0) {
            return '100 %'
        }
        console.log(value1, value2)
        return parseInt(Math.abs(((value1 - value2) / value2) * 100)) + '%'
    }
    const getValueByMonthly = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr,
        dpnames,
        qn_submit_list, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]
        let final_result = {};
        
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: dpnames.map((i) => ({
                    ...refobj,
                    remarks: null,
                    category: null,
                    form_type: 1,
                    current: "",
                    percentage: "",
                    text: "Pending Submission",
                    col: getNumberOfColumn(obj),
                    status: null,
                })),
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                
                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                if (dcfId === 118) {
                    console.log(index2)
                }

                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    dpnames.forEach((dpname) => {
                        let dp_index = qn_submit_list[index].response.findIndex(
                            (item) => item.name === dpname
                        );
                        if (dp_index !== -1) {

                            value = (qn_submit_list[index].response[dp_index].value !== undefined && qn_submit_list[index].response[dp_index].value !== null) ? parseFloat(
                                qn_submit_list[index].response[dp_index].value
                            ) : 0
                        } else {
                            value = 0
                        }
                        let dp_index2 = qn_submit_list[index2].response.findIndex(
                            (item) => item.name === dpname
                        );
                        if (dp_index2 !== -1) {

                            value2 = (qn_submit_list[index2].response[dp_index2].value !== undefined && qn_submit_list[index2].response[dp_index2].value !== null) ? parseFloat(
                                qn_submit_list[index2].response[dp_index2].value
                            ) : 0
                        } else {
                            value = 0
                        }

                        result_arr.push({
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            category: 2,
                            form_type: 1,
                            diff: value === value2 ? null : value > value2 ? false : true,
                            current: value,
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: calculatePercentage(value, value2),
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Requires Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Requires Re-Review"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        });
                    });
                    result = { [obj]: attachObj(result_arr, refobj) };
                } else {
                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];

                    dpnames.forEach((dpname) => {
                        let dp_index = qn_submit_list[index].response.findIndex(
                            (item) => item.name === dpname
                        );
                        if (dp_index !== -1) {
                            console.log(parseFloat(qn_submit_list[index].response[dp_index].value), dcfId, 'finder')
                            value = (qn_submit_list[index].response[dp_index].value !== undefined && qn_submit_list[index].response[dp_index].value !== null) ? parseFloat(
                                qn_submit_list[index].response[dp_index].value
                            ) : '-'
                        } else {
                            value = '-'
                        }
                        result_arr.push({
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            category: 1,
                            form_type: 1,
                            diff: false,
                            current: value,
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Requires Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Requires Re-Review"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        });
                    });
                    result = { [obj]: attachObj(result_arr, refobj) };
                }
            }
            final_result = { ...final_result, ...result };
        });
        
        return final_result;
    };
    const getValueByMonthlyHC = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr,
        dpnames,
        qn_submit_list, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]
        let final_result = {};
        
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [{
                    ...refobj,
                    remarks: null,
                    category: null,
                    form_type: 3,
                    current: "",
                    percentage: "",
                    text: "Pending Submission",
                    col: getNumberOfColumn(obj),
                    status: null,
                }]
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                
                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });

            if (index !== -1) {


                const { type, reject } = qn_submit_list[index];
                let value = null;
                result_arr = [];
                let list_arr = [{
                    remarks: qn_submit_list[index].return_remarks,
                    form_type: 3,
                    col: getNumberOfColumn(obj),
                    id: qn_submit_list[index].id,
                    category: 1,
                    diff: false,
                    current: '',
                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                    percentage: "-",
                    type, self,
                    text:
                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                            (type === 0 && (reject === 1 || reject === 2))
                                ? "Unlocked for Resubmission"
                                : type === 1 && reject === 2
                                    ? "Unlocked for Review Again"
                                    : type === 1
                                        ? "Pending Review"
                                        : type === 2
                                            ? "Pending Approval"
                                            : type === 3
                                                ? "Approved & Locked"
                                                : "Pending Submission",
                    status:
                        type === 0 && (reject === 0 || reject === null) ? 0 :
                            (type === 0 && (reject === 1 || reject === 2))
                                ? 1
                                : type === 1 && reject === 2
                                    ? 2
                                    : type === 1
                                        ? 3
                                        : type === 2
                                            ? 4
                                            : type === 3
                                                ? 5
                                                : null
                }
                ]





                result = { [obj]: attachObj(list_arr, refobj) };

            }
            final_result = { ...final_result, ...result };
        });
        
        return final_result;
    };
    const getValueByMonthlyHazWaste297 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]


        const [a, b, c, x] = ['DPAH0013', 'DPAH0017', 'DPAH0014', 'DPAH0015']
        const DPAH0013Options = [
            { name: 'Used Engine Oil', id: 1 },
            { name: 'Used Lubricating Oil', id: 2 },
            { name: 'Used Oil Filters', id: 3 },
            { name: 'Used Coolant', id: 4 },
            { name: 'Contaminated PPE', id: 5 },
            { name: 'Others', id: 6 }
        ]
        const DPAH0014Options = [
            { name: 'kg', id: 1 },
            { name: 'litre', id: 2 },
            { name: 'number', id: 3 }
        ]
        const DPAH0017Options = [
            { name: 'Incineration (w/o Energy Recovery)', id: 1 },
            { name: 'Incineration (with Energy Recovery)', id: 2 },
            { name: 'Landfill', id: 3 },
            { name: 'Recycle', id: 4 },
            { name: 'Composting', id: 5 },
            { name: 'Waste Recovery', id: 6 },
            { name: 'Unknown', id: 7 },
            { name: 'Others', id: 8 }
        ]
        let final_result = {};

        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                
                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                console.log(index2, obj)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        if (i[a] === 6) {
                            i[a] = i['DPAH0013B']
                        }
                        if (i[b] === 8) {
                            i[b] = i['DPAH0017B']
                        }
                        let ind = list_arr2.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                        if (ind !== -1) {
                            list_arr2[ind][x] += i[x]
                        } else {
                            list_arr2.push(i)
                        }
                    })
                    qn_submit_list[index].response.forEach((i) => {
                        if (i[a] === 6) {
                            i[a] = i['DPAH0013B']
                        }
                        if (i[b] === 8) {
                            i[b] = i['DPAH0017B']
                        }
                        let ind = list_arr.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                        if (ind !== -1) {
                            list_arr[ind][x] += i[x]
                            list_arr[ind].current = list_arr[ind][x]
                            let value = list_arr[ind][x], value2 = null
                            let oldIndex = list_arr2.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr[ind].percentage = calculatePercentage(value, value2)
                                list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                list_arr[ind].category = 2
                            }

                        } else {
                            let value = i[x], value2 = null

                            let oldIndex = list_arr2.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: value === value2 ? null : value > value2 ? false : true,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(value, value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            } else {
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 1,
                                    form_type: 2,
                                    diff: false,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '-',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }

                        }
                    })
                    console.log(qn_submit_list[index], qn_submit_list[index2])
                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {
                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    if (qn_submit_list[index].response.length) {
                        qn_submit_list[index].response.forEach((i) => {
                            if (i[a] === 6) {
                                i[a] = i['DPAH0013B']
                            }
                            if (i[b] === 8) {
                                i[b] = i['DPAH0017B']
                            }
                            let ind = list_arr.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                            if (ind !== -1) {
                                list_arr[ind][x] += i[x]
                                list_arr[ind].current = list_arr[ind][x]
                            } else {
                                list_arr.push(
                                    {
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                            }
                        })
                    } else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }

                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            let find = Object.values(j).filter(i => i[a] !== undefined && i[b] !== undefined && i[c] !== undefined)
            console.log(find, j)
            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {
                    let name1 = k[a]
                    let name2 = k[b]
                    if (typeof k[a] === 'number') {
                        name1 = DPAH0013Options.find(i => i.id === k[a]).name
                    }
                    if (typeof k[b] === 'number') {
                        name2 = DPAH0017Options.find(i => i.id === k[b]).name
                    }

                    let name3 = DPAH0014Options.find(i => i.id === k[c]).name
                    k.dp = (name1 === undefined ? 'NA' : name1) + ' / ' + (name2 === undefined ? 'NA' : name2) + ' ^ ' + name3
                } else {
                    k.dp = null
                }
            })
        })
        return final_result;
    };
    const getValueByMonthlyNonHazWaste307 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]


        const [a, b, c, x] = ['DPAH500', 'DPAH504', 'DPAH501', 'DPAH502']
        let DPAH500Options = [
            { name: "Paper - all types", id: 1 },
            { name: "Plastic - all types", id: 2 },
            { name: "Metals - all types", id: 3 },
            { name: "Glass - all types", id: 11 },
            { name: "Wood - all types", id: 13 },
            { name: "Electrical items - WEEE - (ex. printers)", id: 4 },
            { name: "Electrical items - Batteries", id: 5 },
            { name: "General Waste - Mixed Commecial and industrial waste", id: 6 },
            { name: "General Waste - Organic: mixed food and garden waste", id: 7 },
            { name: "General Waste - Organic: garden waste", id: 8 },
            { name: "General Waste - Organic: food and drink waste", id: 9 },
            { name: "General Waste - Household residual waste", id: 10 },
            { name: "Others ( Please include remarks )", id: 12 },
        ];
        const DPAH501Options = [
            { name: 'kg', id: 1 },
            { name: 'litre', id: 2 },
            { name: 'number', id: 3 }
        ]
        const DPAH504Options = [
            { name: 'Incineration (w/o Energy Recovery)', id: 1 },
            { name: 'Incineration (with Energy Recovery)', id: 2 },
            { name: 'Landfill', id: 3 },
            { name: 'Recycle', id: 4 },
            { name: 'Composting', id: 5 },
            { name: 'Waste Recovery', id: 6 },
            { name: 'Unknown', id: 7 },
            { name: 'Others ( Please include remarks )', id: 8 }
        ]
        let final_result = {};

        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {

                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });

            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {

                    return (
                        i.dcfId === dcfId &&
                        (i.type == 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });

                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        if (i[a] === 12) {
                            i[a] = i['DPAH500B']
                        }
                        if (i[b] === 8) {
                            i[b] = i['DPAH504B']
                        }
                        let ind = list_arr2.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                        if (ind !== -1) {
                            list_arr2[ind][x] += i[x]
                        } else {
                            list_arr2.push(i)
                        }
                    })
                    qn_submit_list[index].response.forEach((i) => {
                        if (i[a] === 12) {
                            i[a] = i['DPAH500B']
                        }
                        if (i[b] === 8) {
                            i[b] = i['DPAH504B']
                        }
                        let ind = list_arr.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                        if (ind !== -1) {
                            list_arr[ind][x] += i[x]
                            list_arr[ind].current = list_arr[ind][x]
                            let value = list_arr[ind][x], value2 = null
                            let oldIndex = list_arr2.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr[ind].percentage = calculatePercentage(value, value2)
                                list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                list_arr[ind].category = 2
                            }

                        } else {
                            let value = i[x], value2 = null

                            let oldIndex = list_arr2.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: value === value2 ? null : value > value2 ? false : true,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(value, value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            } else {
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 1,
                                    form_type: 2,
                                    diff: false,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '-',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }

                        }
                    })

                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {
                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    if (qn_submit_list[index].response.length) {
                        qn_submit_list[index].response.forEach((i) => {
                            if (i[a] === 12) {
                                i[a] = i['DPAH500B']
                            }
                            if (i[b] === 8) {
                                i[b] = i['DPAH504B']
                            }
                            let ind = list_arr.findIndex((j) => j[a] === i[a] && j[b] === i[b] && j[c] === i[c])
                            if (ind !== -1) {
                                list_arr[ind][x] += i[x]
                                list_arr[ind].current = list_arr[ind][x]
                            } else {
                                list_arr.push(
                                    {
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                            }
                        })
                    } else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || !reject) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }

                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            let find = Object.values(j).filter(i => i[a] !== undefined && i[b] !== undefined && i[c] !== undefined)
            console.log(find, j)
            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {
                    let name1 = k[a]
                    let name2 = k[b]
                    if (typeof k[a] === 'number') {
                        name1 = DPAH500Options.find(i => i.id === k[a]).name
                    }
                    if (typeof k[b] === 'number') {
                        name2 = DPAH504Options.find(i => i.id === k[b]).name
                    }

                    let name3 = DPAH501Options.find(i => i.id === k[c]).name
                    k.dp = (name1 === undefined ? 'NA' : name1) + ' / ' + (name2 === undefined ? 'NA' : name2) + ' ^ ' + name3
                } else {
                    k.dp = null
                }
            })
        })
        return final_result;
    };
    const attachObj = (arr, refobj) => {
        for (let i = 0; i < arr.length; i++) {
            arr[i] = { ...arr[i], refobj:refobj.refobj }
        }
        return arr
    }
    const getValueByMonthlyHC3Sub = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr,
        dpnames,
        qn_submit_list, efstdlist, subcat, standardId, categoryId, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]

        let stdindx = efstdlist.findIndex(i => i.id === standardId)

        const [a, b, x] = dpnames

        let final_result = {};
        if (stdindx !== -1) {
            arr.forEach((obj) => {
                console.log(obj)
                let result = {
                    [obj]: [refobj],
                };
                let result_arr = [];
                let index = qn_submit_list.findIndex((i) => {

                    return (
                        i.dcfId === dcfId && i.standard === standardId &&

                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(i.reporting_period) === obj
                    );
                });

                if (index !== -1) {
                    let index2 = qn_submit_list.findIndex((i) => {

                        return (
                            i.dcfId === dcfId && i.standard === standardId &&
                            (i.type == 0 ? i.reject === 1 : true) &&
                            i.tier0_id === tier0 &&
                            i.tier1_id === tier1 &&
                            i.tier2_id === tier2 &&
                            i.tier3_id === tier3 &&
                            getRPTextFormat(
                                [i.reporting_period[i.reporting_period.length - 1]]
                            ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                        );
                    });

                    if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                        const { type1, reject1 } = qn_submit_list[index2];
                        const { type, reject } = qn_submit_list[index];
                        let value = null,
                            value2 = null;
                        result_arr = [];
                        let list_arr = []
                        let list_arr2 = []
                        console.log(qn_submit_list[index2], qn_submit_list[index])
                        qn_submit_list[index2].response.forEach((i) => {
                            let name1 = subcat['cat2'].find(h => h.id === i[a]) === undefined ? 'NA' : subcat['cat2'].find(h => h.id === i[a]).title
                            let name2 = subcat['cat3'].find(h => h.id === i[b]) === undefined ? 'NA' : subcat['cat3'].find(h => h.id === i[b]).title
                            let name3 = subcat['cat1'].find(h => h.id === i['DPA0130']) === undefined ? 'NA' : subcat['cat1'].find(h => h.id === i['DPA0130']).title
                            console.log(name3, subcat['cat1'].find(h => h.id === i['DPA0130']), i['DPA0130'])
                            if (name1.trim().toLowerCase() === 'others') {
                                if (i['DPA0131B'] === 1) {
                                    name1 = i['DPA0131B1']
                                    name2 = i['DPA0131B2']

                                } else {
                                    name1 = 'Others'
                                    name2 = 'NA'
                                }

                            }

                            let ind = list_arr2.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())

                            if (ind !== -1) {
                                list_arr2[ind][x] += i[x]
                            } else {
                                list_arr2.push({ ...i, name1, name2, name3 })
                            }
                        })
                        qn_submit_list[index].response.forEach((i) => {
                            let name1 = subcat['cat2'].find(h => h.id === i[a]) === undefined ? 'NA' : subcat['cat2'].find(h => h.id === i[a]).title
                            let name2 = subcat['cat3'].find(h => h.id === i[b]) === undefined ? 'NA' : subcat['cat3'].find(h => h.id === i[b]).title
                            let name3 = subcat['cat1'].find(h => h.id === i['DPA0130']) === undefined ? 'NA' : subcat['cat1'].find(h => h.id === i['DPA0130']).title
                            if (name1.trim().toLowerCase() === 'others') {
                                if (i['DPA0131B'] === 1) {
                                    name1 = i['DPA0131B1']
                                    name2 = i['DPA0131B2']
                                } else {
                                    name1 = 'Others'
                                    name2 = 'NA'
                                }

                            }
                            let ind = list_arr.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())

                            if (ind !== -1) {
                                list_arr[ind][x] += i[x]
                                list_arr[ind].current = list_arr[ind][x]
                                let value = list_arr[ind][x], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex][x]
                                    list_arr[ind].percentage = calculatePercentage(value, value2)
                                    list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                    list_arr[ind].category = 2
                                }

                            } else {
                                let value = i[x], value2 = null
                                console.log(list_arr2, name3, name2, name1)
                                let oldIndex = list_arr2.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex][x]
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks, name1, name2, name3,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: value === value2 ? null : value > value2 ? false : true,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: calculatePercentage(value, value2),
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks, name1, name2, name3,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 1,
                                        form_type: 2,
                                        diff: false,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '-',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }

                            }
                        })

                        result = { [obj]: attachObj(list_arr, refobj) };
                    } else {
                        const { type, reject } = qn_submit_list[index];
                        let value = null;
                        result_arr = [];
                        let list_arr = []
                        if (qn_submit_list[index].response.length) {
                            qn_submit_list[index].response.forEach((i) => {
                                let name1 = subcat['cat2'].find(h => h.id === i[a]) === undefined ? 'NA' : subcat['cat2'].find(h => h.id === i[a]).title
                                let name2 = subcat['cat3'].find(h => h.id === i[b]) === undefined ? 'NA' : subcat['cat3'].find(h => h.id === i[b]).title
                                let name3 = subcat['cat1'].find(h => h.id === i['DPA0130']) === undefined ? 'NA' : subcat['cat1'].find(h => h.id === i['DPA0130']).title
                                if (name1.trim().toLowerCase() === 'others') {
                                    if (i['DPA0131B'] === 1) {
                                        name1 = i['DPA0131B1']
                                        name2 = i['DPA0131B2']
                                    } else {
                                        name1 = 'Others'
                                        name2 = 'NA'
                                    }

                                }

                                let ind = list_arr.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())

                                if (ind !== -1) {
                                    list_arr[ind][x] += i[x]
                                    list_arr[ind].current = list_arr[ind][x]
                                } else {
                                    list_arr.push(
                                        {
                                            ...i, remarks: qn_submit_list[index].return_remarks, name1, name2, name3,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            form_type: 2,
                                            category: 1,
                                            diff: false,
                                            current: i[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: "-",
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || !reject) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })
                                }
                            })
                        } else {
                            list_arr.push({
                                edit: 1,
                                remarks: qn_submit_list[index].return_remarks,
                                col: getNumberOfColumn(obj),
                                id: qn_submit_list[index].id,
                                form_type: 2,
                                category: 1,
                                diff: false,
                                current: '-',
                                month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                percentage: "-",
                                type, self,
                                text:
                                    type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? "Unlocked for Resubmission"
                                            : type === 1 && reject === 2
                                                ? "Unlocked for Review Again"
                                                : type === 1
                                                    ? "Pending Review"
                                                    : type === 2
                                                        ? "Pending Approval"
                                                        : type === 3
                                                            ? "Approved & Locked"
                                                            : "Pending Submission",
                                status:
                                    type === 0 && (reject === 0 || !reject) ? 0 :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? 1
                                            : type === 1 && reject === 2
                                                ? 2
                                                : type === 1
                                                    ? 3
                                                    : type === 2
                                                        ? 4
                                                        : type === 3
                                                            ? 5
                                                            : null
                            })
                        }

                        result = { [obj]: attachObj(list_arr, refobj) };
                    }
                }

                final_result = { ...final_result, ...result };
            });
        } else {
            arr.forEach((obj) => {
                let result = {
                    [obj]: [],
                };
                final_result = { ...final_result, ...result };
            })

        }

        Object.values(final_result).forEach((j) => {
            let find = Object.values(j).filter(i => i[a] !== undefined && i[b] !== undefined)
            console.log(find, j)
            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {

                    k.dp = k.name3 + '/' + k.name1 + ' ^ ' + k.name2


                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    const getValueByMonthlyElectricity287 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr,
        dpnames,
        qn_submit_list, efstdlist, subcat, standardId, categoryId, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]

        let stdindx = efstdlist.findIndex(i => i.id === standardId)

        const [a, b, c, x] = dpnames
        console.log(stdindx, standardId)
        let final_result = {};

        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {

                return (
                    i.dcfId === dcfId && i.standard === standardId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });

            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {

                    return (
                        i.dcfId === dcfId && i.standard === standardId &&
                        (i.type == 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
console.log(index2)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let name1 = subcat['cat1'].find(h => h.id === i[a]) === undefined ? 'NA' : subcat['cat1'].find(h => h.id === i[a]).title
                        let name2 = '', name3 = '', energyval = 0
                        if (!name1.trim().toLowerCase().includes('grid')) {
                            let name_1 = subcat['cat2'].find(h => h.id === i[b]) === undefined ? 'NA' : subcat['cat2'].find(h => h.id === i[b]).title
                            let name_2 = subcat['cat3'].find(h => h.id === i[c]) === undefined ? 'NA' : subcat['cat3'].find(h => h.id === i[c]).title
                            if (name_1.trim().toLowerCase().includes('others')) {
                                name2 = 'Others (' + i['DPAN095A1'] + ')'
                            } else {
                                name2 = name_1
                            }
                            if (name_2.trim().toLowerCase().includes('others')) {
                                name3 = 'Others (' + i['DPAN095B1'] + ')'
                            } else {
                                name3 = name_2
                            }
                            energyval = i['DPAN098B']
                        } else {
                            energyval = i['DPAN098A']
                        }


                        let ind = list_arr2.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())

                        if (ind !== -1) {
                            list_arr2[ind][x] += energyval
                        } else {
                            list_arr2.push({ ...i, name1, name2, name3, energyval })
                        }
                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let name1 = subcat['cat1'].find(h => h.id === i[a]) === undefined ? 'NA' : subcat['cat1'].find(h => h.id === i[a]).title
                        let name2 = '', name3 = '', energyval = 0
                        if (!name1.trim().toLowerCase().includes('grid')) {
                            let name_1 = subcat['cat2'].find(h => h.id === i[b]) === undefined ? 'NA' : subcat['cat2'].find(h => h.id === i[b]).title
                            let name_2 = subcat['cat3'].find(h => h.id === i[c]) === undefined ? 'NA' : subcat['cat3'].find(h => h.id === i[c]).title
                            if (name_1.trim().toLowerCase().includes('others')) {
                                name2 = 'Others (' + i['DPAN095A1'] + ')'
                            } else {
                                name2 = name_1
                            }
                            if (name_2.trim().toLowerCase().includes('others')) {
                                name3 = 'Others (' + i['DPAN095B1'] + ')'
                            } else {
                                name3 = name_2
                            }
                            energyval = i['DPAN098B']
                        } else {
                            energyval = i['DPAN098A']
                        }
                        let ind = list_arr.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())

                        if (ind !== -1) {
                            list_arr[ind][x] += energyval
                            list_arr[ind].current = list_arr[ind][x]
                            let value = list_arr[ind][x], value2 = null
                            let oldIndex = list_arr2.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr[ind].percentage = calculatePercentage(value, value2)
                                list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                list_arr[ind].category = 2
                            }

                        } else {
                            let value = energyval, value2 = null
                            let oldIndex = list_arr2.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks, name1, name2, name3, energyval,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: value === value2 ? null : value > value2 ? false : true,
                                    current: energyval,
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(value, value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            } else {
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks, name1, name2, name3, energyval,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 1,
                                    form_type: 2,
                                    diff: false,
                                    current: energyval,
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '-',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }

                        }
                    })

                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {
                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    if (qn_submit_list[index].response.length) {
                        qn_submit_list[index].response.forEach((i) => {
                            let name1 = subcat['cat1'].find(h => h.id === i[a]) === undefined ? 'NA' : subcat['cat1'].find(h => h.id === i[a]).title
                            let name2 = '', name3 = '', energyval = 0
                            if (!name1.trim().toLowerCase().includes('grid')) {
                                let name_1 = subcat['cat2'].find(h => h.id === i[b]) === undefined ? 'NA' : subcat['cat2'].find(h => h.id === i[b]).title
                                let name_2 = subcat['cat3'].find(h => h.id === i[c]) === undefined ? 'NA' : subcat['cat3'].find(h => h.id === i[c]).title
                                if (name_1.trim().toLowerCase().includes('others')) {
                                    name2 = 'Others (' + i['DPAN095A1'] + ')'
                                } else {
                                    name2 = name_1
                                }
                                if (name_2.trim().toLowerCase().includes('others')) {
                                    name3 = 'Others (' + i['DPAN095B1'] + ')'
                                } else {
                                    name3 = name_2
                                }
                                energyval = i['DPAN098B']
                            } else {
                                energyval = i['DPAN098A']
                            }
                            console.log(name1, name2, name3, energyval)
                            let ind = list_arr.findIndex((j) => j['name3'].trim().toLowerCase() === name3.trim().toLowerCase() && j['name1'].trim().toLowerCase() === name1.trim().toLowerCase() && j['name2'].trim().toLowerCase() === name2.trim().toLowerCase())

                            if (ind !== -1) {
                                list_arr[ind][x] += energyval
                                list_arr[ind].current = list_arr[ind][x]
                            } else {
                                list_arr.push(
                                    {
                                        ...i, remarks: qn_submit_list[index].return_remarks, name1, name2, name3, energyval,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: energyval,
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                            }
                        })
                    } else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || !reject) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }

                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });


        Object.values(final_result).forEach((j) => {
            let find = Object.values(j).filter(i => i[a] !== undefined)
            console.log(find, j)
            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {
                    if (k['name2'].trim().length) {
                        k.dp = k.name1 + '/' + k.name2 + '/' + k.name3 + ' ^ ' + 'kWh'
                    } else {
                        k.dp = k.name1 + ' ^ ' + 'kWh'
                    }



                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    function getOverdueDays(monthString) {
        console.log(monthString)
        const [startMonth, endMonth] = monthString.split(' to ');

        const month = endMonth ? endMonth : startMonth;
        const [monthValue, year] = month.split('-');
        const endOfMonth = DateTime.fromObject({ year: parseInt(year), month: DateTime.fromFormat(monthValue, 'LLL').month }).endOf('month');
        const currentDate = DateTime.local();
        console.log(month, endOfMonth.diff(currentDate, 'days').days)
        return endOfMonth.diff(currentDate, 'days').days;
    }
    const getValueByMonthlyHC1Sub = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr,
        dpnames,
        qn_submit_list, efstdlist, subcat, standardId, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]

        let stdindx = efstdlist.findIndex(i => i.id === standardId)

        const [a, x] = dpnames

        let final_result = {};
        if (stdindx !== -1) {
            arr.forEach((obj) => {
                console.log(obj)
                let result = {
                    [obj]: [refobj],
                };
                let result_arr = [];
                let index = qn_submit_list.findIndex((i) => {

                    return (
                        i.dcfId === dcfId && i.standard === standardId &&

                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(i.reporting_period) === obj
                    );
                });

                if (index !== -1) {
                    let index2 = qn_submit_list.findIndex((i) => {

                        return (
                            i.dcfId === dcfId && i.standard === standardId &&
                            (i.type == 0 ? i.reject === 1 : true) &&
                            i.tier0_id === tier0 &&
                            i.tier1_id === tier1 &&
                            i.tier2_id === tier2 &&
                            i.tier3_id === tier3 &&
                            getRPTextFormat(
                                [i.reporting_period[i.reporting_period.length - 1]]
                            ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                        );
                    });

                    if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                        const { type1, reject1 } = qn_submit_list[index2];
                        const { type, reject } = qn_submit_list[index];
                        let value = null,
                            value2 = null;
                        result_arr = [];
                        let list_arr = []
                        let list_arr2 = []
                        qn_submit_list[index2].response.forEach((i) => {
                            let ind = list_arr2.findIndex((j) => j[a] === i[a])
                            if (ind !== -1) {
                                list_arr2[ind][x] += i[x]
                            } else {
                                list_arr2.push(i)
                            }
                        })
                        qn_submit_list[index].response.forEach((i) => {
                            let ind = list_arr.findIndex((j) => j[a] === i[a])
                            if (ind !== -1) {
                                list_arr[ind][x] += i[x]
                                list_arr[ind].current = list_arr[ind][x]
                                let value = list_arr[ind][x], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j[a] === i[a])
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex][x]
                                    list_arr[ind].percentage = calculatePercentage(value, value2)
                                    list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                    list_arr[ind].category = 2
                                }

                            } else {
                                let value = i[x], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j[a] === i[a])
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex][x]
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: value === value2 ? null : value > value2 ? false : true,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: calculatePercentage(value, value2),
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 1,
                                        form_type: 2,
                                        diff: false,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '-',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }

                            }
                        })

                        result = { [obj]: attachObj(list_arr, refobj) };
                    } else {
                        const { type, reject } = qn_submit_list[index];
                        let value = null;
                        result_arr = [];
                        let list_arr = []
                        if (qn_submit_list[index].response.length) {
                            qn_submit_list[index].response.forEach((i) => {
                                let ind = list_arr.findIndex((j) => j[a] === i[a])
                                if (ind !== -1) {
                                    list_arr[ind][x] += i[x]
                                    list_arr[ind].current = list_arr[ind][x]
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: i[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }
                            })
                        }
                        else {
                            list_arr.push({
                                edit: 1,
                                remarks: qn_submit_list[index].return_remarks,
                                col: getNumberOfColumn(obj),
                                id: qn_submit_list[index].id,
                                form_type: 2,
                                category: 1,
                                diff: false,
                                current: '-',
                                month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                percentage: "-",
                                type, self,
                                text:
                                    type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? "Unlocked for Resubmission"
                                            : type === 1 && reject === 2
                                                ? "Unlocked for Review Again"
                                                : type === 1
                                                    ? "Pending Review"
                                                    : type === 2
                                                        ? "Pending Approval"
                                                        : type === 3
                                                            ? "Approved & Locked"
                                                            : "Pending Submission",
                                status:
                                    type === 0 && (reject === 0 || !reject) ? 0 :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? 1
                                            : type === 1 && reject === 2
                                                ? 2
                                                : type === 1
                                                    ? 3
                                                    : type === 2
                                                        ? 4
                                                        : type === 3
                                                            ? 5
                                                            : null
                            })
                        }



                        result = { [obj]: attachObj(list_arr, refobj) };
                    }
                }

                final_result = { ...final_result, ...result };
            });
        } else {
            arr.forEach((obj) => {
                let result = {
                    [obj]: [],
                };
                final_result = { ...final_result, ...result };
            })

        }
        Object.values(final_result).forEach((j) => {
            let find = Object.values(j).filter(i => i[a] !== undefined)
            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {
                    let name1 = subcat['cat1'].find(i => i.id === k[a])

                    k.dp = (name1 === undefined ? 'NA' : name1.title + ' ^ ' + unit)
                } else {
                    k.dp = null
                }
            })
        })
        return final_result;
    };
    const getValueByMonthlyWaterD286 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]
        const [a, x] = ['DPAN0052', 'DPAN0053']
        const DPAN0052Options = [
            { name: "Surface water", id: 1 },
            { name: "Ground Water", id: 2 },
            { name: "Sea Water", id: 3 },
            { name: "Third-Party Water", id: 5 },
            { name: "Water Reuse", id: 6 },
            { name: "Water Recycled", id: 7 },
        ];


        let final_result = {};

        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {

                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });

            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {

                    return (
                        i.dcfId === dcfId &&
                        (i.type == 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });

                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let ind = list_arr2.findIndex((j) => j[a] === i[a])
                        if (ind !== -1) {
                            list_arr2[ind][x] += i[x]
                        } else {
                            list_arr2.push(i)
                        }
                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let ind = list_arr.findIndex((j) => j[a] === i[a])
                        if (ind !== -1) {
                            list_arr[ind][x] += i[x]
                            list_arr[ind].current = list_arr[ind][x]
                            let value = list_arr[ind][x], value2 = null
                            let oldIndex = list_arr2.findIndex((j) => j[a] === i[a])
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr[ind].percentage = calculatePercentage(value, value2)
                                list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                list_arr[ind].category = 2
                            }

                        } else {
                            let value = i[x], value2 = null
                            let oldIndex = list_arr2.findIndex((j) => j[a] === i[a])
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: value === value2 ? null : value > value2 ? false : true,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(value, value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            } else {
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 1,
                                    form_type: 2,
                                    diff: false,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '-',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }

                        }
                    })

                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {
                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    if (qn_submit_list[index].response.length) {
                        qn_submit_list[index].response.forEach((i) => {
                            let ind = list_arr.findIndex((j) => j[a] === i[a])
                            if (ind !== -1) {
                                list_arr[ind][x] += i[x]
                                list_arr[ind].current = list_arr[ind][x]
                            } else {
                                list_arr.push({
                                    ...i, remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    form_type: 2,
                                    category: 1,
                                    diff: false,
                                    current: i[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: "-",
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || !reject) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }
                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || !reject) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            let find = Object.values(j).filter(i => i[a] !== undefined)
            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {
                    let name1 = DPAN0052Options.find(i => i.id === k[a])

                    k.dp = (name1 === undefined ? 'NA' : 'Quantity of ' + name1.name + ' Discharge' + ' ^ ' + unit)
                } else {
                    k.dp = null
                }
            })
        })
        return final_result;
    };
    const getValueByMonthlyWaterW285 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]

        const DPAN0048Options = [
            { name: "Self Generated", id: 6 },

            { name: "Third-Party Water", id: 5 },
        ];
        const DPAN1158Options = [
            { name: "Groundwater Wells Operated", id: 1 },

            { name: "Effluent/ Sewage Treatment Recycle", id: 2 },
            { name: 'Rainwater Harvesting', id: 3 },
            { name: 'Others', id: 99 }
        ];
        const DPAN1200Options = [
            { name: "Surface water", id: 1 },
            { name: "Ground Water", id: 2 },
            { name: "Sea Water", id: 3 },
            { name: 'Imported Water from Industrial District', id: 8 },
            { name: 'Unknown', id: 9 }
        ];

        let final_result = {};

        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {

                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });

            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {

                    return (
                        i.dcfId === dcfId &&
                        (i.type == 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });

                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        if (i['DPAN0048'] === 6) {
                            let ind = list_arr2.findIndex((j) => j['DPAN1158'] === i['DPAN1158'])

                            if (ind !== -1) {
                                list_arr2[ind]['DPAN1159'] += i['DPAN1159']
                            } else {
                                list_arr2.push(i)
                            }
                        } else if (i['DPAN0048'] === 5) {
                            let ind = list_arr2.findIndex((j) => j['DPAN1200'] === i['DPAN1200'])

                            if (ind !== -1) {
                                list_arr2[ind]['DPAN1161'] += i['DPAN1161']
                            } else {
                                list_arr2.push(i)
                            }
                        }

                    })
                    qn_submit_list[index].response.forEach((i) => {
                        if (i['DPAN0048'] === 6) {
                            let ind = list_arr.findIndex((j) => j['DPAN1158'] === i['DPAN1158'])
                            if (ind !== -1) {
                                list_arr[ind]['DPAN1159'] += i['DPAN1159']
                                list_arr[ind].current = list_arr[ind]['DPAN1159']
                                let value = list_arr[ind]['DPAN1159'], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j['DPAN1158'] === i['DPAN1158'])
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex]['DPAN1159']
                                    list_arr[ind].percentage = calculatePercentage(value, value2)
                                    list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                    list_arr[ind].category = 2
                                }

                            } else {
                                let value = i['DPAN1159'], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j['DPAN1158'] === i['DPAN1158'])
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex]['DPAN1159']
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: value === value2 ? null : value > value2 ? false : true,
                                        current: i['DPAN1159'],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: calculatePercentage(value, value2),
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 1,
                                        form_type: 2,
                                        diff: false,
                                        current: i['DPAN1159'],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '-',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }

                            }
                        } else if (i['DPAN0048'] === 5) {
                            let ind = list_arr.findIndex((j) => j['DPAN1200'] === i['DPAN1200'])
                            if (ind !== -1) {
                                list_arr[ind]['DPAN1161'] += i['DPAN1161']
                                list_arr[ind].current = list_arr[ind]['DPAN1161']
                                let value = list_arr[ind]['DPAN1161'], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j['DPAN1200'] === i['DPAN1200'])
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex]['DPAN1161']
                                    list_arr[ind].percentage = calculatePercentage(value, value2)
                                    list_arr[ind].diff = value === value2 ? null : value > value2 ? false : true
                                    list_arr[ind].category = 2
                                }

                            } else {
                                let value = i['DPAN1161'], value2 = null
                                let oldIndex = list_arr2.findIndex((j) => j['DPAN1200'] === i['DPAN1200'])
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex]['DPAN1161']
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: value === value2 ? null : value > value2 ? false : true,
                                        current: i['DPAN1161'],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: calculatePercentage(value, value2),
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 1,
                                        form_type: 2,
                                        diff: false,
                                        current: i['DPAN1161'],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '-',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }

                            }
                        }

                    })

                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {
                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    if (qn_submit_list[index].response.length) {
                        qn_submit_list[index].response.forEach((i) => {
                            if (i['DPAN0048'] === 6) {
                                let ind = list_arr.findIndex((j) => j['DPAN1158'] === i['DPAN1158'])
                                if (ind !== -1) {
                                    list_arr[ind]['DPAN1159'] += i['DPAN1159']
                                    list_arr[ind].current = list_arr[ind]['DPAN1159']
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: i['DPAN1159'],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }
                            } else if (i['DPAN0048'] === 5) {
                                let ind = list_arr.findIndex((j) => j['DPAN1200'] === i['DPAN1200'])
                                if (ind !== -1) {
                                    list_arr[ind]['DPAN1161'] += i['DPAN1161']
                                    list_arr[ind].current = list_arr[ind]['DPAN1161']
                                } else {
                                    list_arr.push({
                                        ...i, remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: i['DPAN1161'],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || !reject) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }
                            }

                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || !reject) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || !reject) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            console.log(j)
            let find = Object.values(j).filter(i => (i['DPAN0048'] !== undefined));

            Object.values(j).forEach((k) => {
                if (k.category !== null && find.length) {
                    if (k['DPAN0048'] === 6) {
                        let name1 = DPAN1158Options.find(i => i.id === k['DPAN1158'])

                        k.dp = (name1 === undefined ? 'NA' : 'Quantity of ' + name1.name + ' withdrawal' + ' ^ ' + unit)

                    } else if (k['DPAN0048'] === 5) {
                        let name1 = DPAN1200Options.find(i => i.id === k['DPAN1200'])

                        k.dp = (name1 === undefined ? 'NA' : 'Quantity of ' + name1.name + ' withdrawal' + ' ^ ' + unit)

                    }

                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    const getValueByMonthlyBTAir = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]

        const value_arr = ['a1', 'b1', 'c1']
        let final_result = {};
        const DPA0296Options = [
            { name: "Economy Class", id: 325 },
            { name: "Premium Economy Class", id: 324 },
            { name: "Business / First Class", id: 323 },
        ]
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                
                return (
                    i.dcfId === dcfId &&

                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                console.log(index2, obj)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let abc = {}
                        if (i['DPAN1304'] === 1) {
                            abc['a1'] = i['DPAN1300'];
                            abc['b1'] = i['DPAN1301'];
                            abc['c1'] = i['DPAN1302'];

                            value_arr.forEach((x) => {
                                list_arr2.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 1,
                                    form_type: 2,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '-',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            })
                        } else if (i['DPAN1304'] === 2) {
                            let abc = {}
                            if (i.legs.length) {
                                i.legs.forEach((j) => {
                                    let sel = ''
                                    if (j['DPA0296'] === 323) {
                                        sel = 'a1'
                                    } else if (j['DPA0296'] === 324) {
                                        sel = 'b1'
                                    } else if (j['DPA0296'] === 325) {
                                        sel = 'c1'
                                    }


                                    if (abc[sel]) {
                                        abc[sel] += j.air_km

                                    } else {
                                        abc[sel] = j.air_km

                                    }
                                })
                            }

                            value_arr.forEach((x) => {
                                if (abc[x]) {
                                    list_arr2.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 1,
                                        form_type: 2,
                                        diff: false,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '-',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }

                            })
                        }

                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let abc = {}
                        if (i['DPAN1304'] === 1) {
                            abc['a1'] = i['DPAN1300'];
                            abc['b1'] = i['DPAN1301'];
                            abc['c1'] = i['DPAN1302'];

                            value_arr.forEach((x) => {
                                console.log(list_arr2)
                                let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex][x]

                                    list_arr.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: calculatePercentage(abc[x], value2),
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })

                                } else {

                                    list_arr.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: false,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '100%',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }
                            })



                        } else if (i['DPAN1304'] === 2) {
                            let list_air = []
                            i.legs.forEach((j) => {
                                let sel = ''
                                if (j['DPA0296'] === 323) {
                                    sel = 'a1'
                                } else if (j['DPA0296'] === 324) {
                                    sel = 'b1'
                                } else if (j['DPA0296'] === 325) {
                                    sel = 'c1'
                                }


                                if (abc[sel]) {
                                    abc[sel] += j.air_km

                                } else {
                                    abc[sel] = j.air_km

                                }
                            })
                            value_arr.forEach((x) => {
                                if (abc[x]) {
                                    let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                                    if (oldIndex !== -1) {
                                        value2 = list_arr2[oldIndex][x]
                                        list_arr.push({
                                            ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            category: 2,
                                            form_type: 2,
                                            diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                            current: abc[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: calculatePercentage(abc[x], value2),
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })

                                    } else {
                                        list_arr.push({
                                            ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            category: 2,
                                            form_type: 2,
                                            diff: false,
                                            current: abc[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: '100%',
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })
                                    }
                                }
                            })
                        }

                    })


                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {

                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    console.log(qn_submit_list[index])
                    if (qn_submit_list[index].response.length) {

                        qn_submit_list[index].response.forEach((i) => {
                            let abc = {}
                            if (i['DPAN1304'] === 1) {
                                abc['a1'] = i['DPAN1300'];
                                abc['b1'] = i['DPAN1301'];
                                abc['c1'] = i['DPAN1302'];
                                value_arr.forEach((x) => {
                                    list_arr.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                })


                            } else if (i['DPAN1304'] === 2) {
                                let list_air = []
                                if (i.legs.length) {
                                    i.legs.forEach((j) => {
                                        let sel = ''
                                        if (j['DPA0296'] === 323) {
                                            sel = 'a1'
                                        } else if (j['DPA0296'] === 324) {
                                            sel = 'b1'
                                        } else if (j['DPA0296'] === 325) {
                                            sel = 'c1'
                                        }
                                        console.log(i.legs, sel, i)

                                        if (i[sel]) {
                                            i[sel] += j.air_km

                                        } else {
                                            i[sel] = j.air_km

                                        }
                                    })
                                }
                                value_arr.forEach((x) => {
                                    if (i[x] || i[x] === 0) {
                                        list_arr.push({
                                            ...i, remarks: qn_submit_list[index].return_remarks,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            form_type: 2,
                                            category: 1,
                                            diff: false,
                                            current: i[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: "-",
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })
                                    }
                                })



                            }

                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            console.log(j, arr)
            let find = Object.values(j).filter(i => (i['a1'] !== undefined || i['b1'] !== undefined || i['c1'] !== undefined));

            Object.values(j).forEach((k) => {
                console.log(k)
                if (k.category !== null && find.length) {
                    let name1 = 'NA'
                    if (k['a1'] || k['a1'] === 0) {
                        name1 = DPA0296Options.find(i => i.id === 323)
                    } else if (k['b1'] || k['b1'] === 0) {
                        name1 = DPA0296Options.find(i => i.id === 324)
                    } else if (k['c1'] || k['c1'] === 0) {
                        name1 = DPA0296Options.find(i => i.id === 325)
                    }


                    k.dp = (name1 === undefined ? 'NA' : name1.name + ' ^ ' + unit)



                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    const getValueByMonthlyBTRail = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]

        const value_arr = ['a1', 'b1', 'c1']
        let final_result = {};
        const DPAN1312Options = [{ name: 'Intercity Rail', id: 332 }, { name: 'Commuter Rail', id: 333 }, { name: 'Transit Rail', id: 334 }]
        console.log(tier0, tier1, tier2, tier3)
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                console.log(getRPTextFormat(i.reporting_period) === obj, dcfId === i.dcfId, i.tier0_id === tier0,
                    i.tier1_id === tier1,
                    i.tier2_id === tier2,
                    i.tier3_id === tier3, i.id, i.tier3_id, tier3);
                return (
                    i.dcfId === dcfId &&
                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                console.log(index2, obj)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let abc = {}
                        if (i['DPAN1313'] === 1) {
                            abc['a1'] = i['DPAN1306'];
                            abc['b1'] = i['DPAN1307'];
                            abc['c1'] = i['DPAN1308'];

                            value_arr.forEach((x) => {
                                list_arr2.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 1,
                                    form_type: 2,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '-',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            })
                        } else if (i['DPAN1313'] === 2) {
                            let abc = {}
                            if (i.legs.length) {
                                i.legs.forEach((j) => {
                                    let sel = ''
                                    if (j['DPAN1312'] === 332) {
                                        sel = 'a1'
                                    } else if (j['DPAN1312'] === 333) {
                                        sel = 'b1'
                                    } else if (j['DPAN1312'] === 334) {
                                        sel = 'c1'
                                    }


                                    if (abc[sel]) {
                                        abc[sel] += j.rail_km

                                    } else {
                                        abc[sel] = j.rail_km

                                    }
                                })
                            }

                            value_arr.forEach((x) => {
                                if (abc[x]) {
                                    list_arr2.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 1,
                                        form_type: 2,
                                        diff: false,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '-',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }

                            })
                        }

                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let abc = {}
                        if (i['DPAN1313'] === 1) {
                            abc['a1'] = i['DPAN1306'];
                            abc['b1'] = i['DPAN1307'];
                            abc['c1'] = i['DPAN1308'];

                            value_arr.forEach((x) => {
                                console.log(list_arr2)
                                let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                                if (oldIndex !== -1) {
                                    value2 = list_arr2[oldIndex][x]

                                    list_arr.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: calculatePercentage(abc[x], value2),
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })

                                } else {

                                    list_arr.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        category: 2,
                                        form_type: 2,
                                        diff: false,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: '100%',
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                }
                            })



                        } else if (i['DPAN1313'] === 2) {
                            let list_air = []
                            i.legs.forEach((j) => {
                                let sel = ''
                                if (j['DPAN1312'] === 332) {
                                    sel = 'a1'
                                } else if (j['DPAN1312'] === 333) {
                                    sel = 'b1'
                                } else if (j['DPAN1312'] === 334) {
                                    sel = 'c1'
                                }


                                if (abc[sel]) {
                                    abc[sel] += j.rail_km

                                } else {
                                    abc[sel] = j.rail_km

                                }
                            })
                            value_arr.forEach((x) => {
                                if (abc[x]) {
                                    let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                                    if (oldIndex !== -1) {
                                        value2 = list_arr2[oldIndex][x]
                                        list_arr.push({
                                            ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            category: 2,
                                            form_type: 2,
                                            diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                            current: abc[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: calculatePercentage(abc[x], value2),
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })

                                    } else {
                                        list_arr.push({
                                            ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            category: 2,
                                            form_type: 2,
                                            diff: false,
                                            current: abc[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: '100%',
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })
                                    }
                                }
                            })
                        }

                    })


                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {

                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    console.log(qn_submit_list[index])
                    if (qn_submit_list[index].response.length) {

                        qn_submit_list[index].response.forEach((i) => {
                            let abc = {}
                            if (i['DPAN1313'] === 1) {
                                abc['a1'] = i['DPAN1306'];
                                abc['b1'] = i['DPAN1307'];
                                abc['c1'] = i['DPAN1308'];
                                value_arr.forEach((x) => {
                                    list_arr.push({
                                        ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                        col: getNumberOfColumn(obj),
                                        id: qn_submit_list[index].id,
                                        form_type: 2,
                                        category: 1,
                                        diff: false,
                                        current: abc[x],
                                        month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                        percentage: "-",
                                        type, self,
                                        text:
                                            type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? "Unlocked for Resubmission"
                                                    : type === 1 && reject === 2
                                                        ? "Unlocked for Review Again"
                                                        : type === 1
                                                            ? "Pending Review"
                                                            : type === 2
                                                                ? "Pending Approval"
                                                                : type === 3
                                                                    ? "Approved & Locked"
                                                                    : "Pending Submission",
                                        status:
                                            type === 0 && (reject === 0 || reject === null) ? 0 :
                                                (type === 0 && (reject === 1 || reject === 2))
                                                    ? 1
                                                    : type === 1 && reject === 2
                                                        ? 2
                                                        : type === 1
                                                            ? 3
                                                            : type === 2
                                                                ? 4
                                                                : type === 3
                                                                    ? 5
                                                                    : null
                                    })
                                })


                            } else if (i['DPAN1313'] === 2) {
                                let list_air = []
                                if (i.legs.length) {
                                    i.legs.forEach((j) => {
                                        let sel = ''
                                        if (j['DPAN1312'] === 332) {
                                            sel = 'a1'
                                        } else if (j['DPAN1312'] === 333) {
                                            sel = 'b1'
                                        } else if (j['DPAN1312'] === 334) {
                                            sel = 'c1'
                                        }
                                        console.log(i.legs, sel, i)

                                        if (i[sel]) {
                                            i[sel] += j.rail_km

                                        } else {
                                            i[sel] = j.rail_km

                                        }
                                    })
                                }
                                value_arr.forEach((x) => {
                                    if (i[x] || i[x] === 0) {
                                        list_arr.push({
                                            ...i, remarks: qn_submit_list[index].return_remarks,
                                            col: getNumberOfColumn(obj),
                                            id: qn_submit_list[index].id,
                                            form_type: 2,
                                            category: 1,
                                            diff: false,
                                            current: i[x],
                                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                            percentage: "-",
                                            type, self,
                                            text:
                                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? "Unlocked for Resubmission"
                                                        : type === 1 && reject === 2
                                                            ? "Unlocked for Review Again"
                                                            : type === 1
                                                                ? "Pending Review"
                                                                : type === 2
                                                                    ? "Pending Approval"
                                                                    : type === 3
                                                                        ? "Approved & Locked"
                                                                        : "Pending Submission",
                                            status:
                                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                                    (type === 0 && (reject === 1 || reject === 2))
                                                        ? 1
                                                        : type === 1 && reject === 2
                                                            ? 2
                                                            : type === 1
                                                                ? 3
                                                                : type === 2
                                                                    ? 4
                                                                    : type === 3
                                                                        ? 5
                                                                        : null
                                        })
                                    }
                                })



                            }

                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            console.log(j, arr)
            let find = Object.values(j).filter(i => (i['a1'] !== undefined || i['b1'] !== undefined || i['c1'] !== undefined));

            Object.values(j).forEach((k) => {
                console.log(k)
                if (k.category !== null && find.length) {
                    let name1 = 'NA'
                    if (k['a1'] || k['a1'] === 0) {
                        name1 = DPAN1312Options.find(i => i.id === 332)
                    } else if (k['b1'] || k['b1'] === 0) {
                        console.log('xyz')
                        name1 = DPAN1312Options.find(i => i.id === 333)
                    } else if (k['c1'] || k['c1'] === 0) {
                        name1 = DPAN1312Options.find(i => i.id === 334)
                    }


                    k.dp = (name1 === undefined ? 'NA' : name1.name + ' ^ ' + unit)



                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    const getValueByMonthlyBTLand = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]
        let land_veh_list = ['Standard Taxi / Sedan', 'Hybrid Vehicle', 'Executive Taxi', 'Dual Purpose 4 x 4 (SUV)', 'Electric Vehicle (EV)', 'Van/ Coach']
        const value_arr = ['a1', 'b1', 'c1', 'd1', 'e1', 'f1']
        let final_result = {};

        console.log(tier0, tier1, tier2, tier3)
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                console.log(getRPTextFormat(i.reporting_period) === obj, dcfId === i.dcfId, i.tier0_id === tier0,
                    i.tier1_id === tier1,
                    i.tier2_id === tier2,
                    i.tier3_id === tier3, i.id, i.tier3_id, tier3);
                return (
                    i.dcfId === dcfId &&
                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                console.log(index2, obj)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let abc = {}

                        abc['a1'] = i['DPAN1314'];
                        abc['b1'] = i['DPAN1315'];
                        abc['c1'] = i['DPAN1316'];
                        abc['d1'] = i['DPAN1317'];
                        abc['e1'] = i['DPAN1318'];
                        abc['f1'] = i['DPAN1319'];
                        value_arr.forEach((x) => {
                            list_arr2.push({
                                ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                col: getNumberOfColumn(obj),
                                id: qn_submit_list[index].id,
                                category: 1,
                                form_type: 2,
                                diff: false,
                                current: abc[x],
                                month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                percentage: '-',
                                type, self,
                                text:
                                    type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? "Unlocked for Resubmission"
                                            : type === 1 && reject === 2
                                                ? "Unlocked for Review Again"
                                                : type === 1
                                                    ? "Pending Review"
                                                    : type === 2
                                                        ? "Pending Approval"
                                                        : type === 3
                                                            ? "Approved & Locked"
                                                            : "Pending Submission",
                                status:
                                    type === 0 && (reject === 0 || reject === null) ? 0 :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? 1
                                            : type === 1 && reject === 2
                                                ? 2
                                                : type === 1
                                                    ? 3
                                                    : type === 2
                                                        ? 4
                                                        : type === 3
                                                            ? 5
                                                            : null
                            })
                        })


                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let abc = {}

                        abc['a1'] = i['DPAN1306'];
                        abc['b1'] = i['DPAN1307'];
                        abc['c1'] = i['DPAN1308'];

                        value_arr.forEach((x) => {
                            console.log(list_arr2)
                            let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]

                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(abc[x], value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })

                            } else {

                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '100%',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }
                        })





                    })


                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {

                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    console.log(qn_submit_list[index])
                    if (qn_submit_list[index].response.length) {

                        qn_submit_list[index].response.forEach((i) => {
                            let abc = {}

                            abc['a1'] = i['DPAN1314'];
                            abc['b1'] = i['DPAN1315'];
                            abc['c1'] = i['DPAN1316'];
                            abc['d1'] = i['DPAN1317'];
                            abc['e1'] = i['DPAN1318'];
                            abc['f1'] = i['DPAN1319'];
                            value_arr.forEach((x) => {
                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    form_type: 2,
                                    category: 1,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: "-",
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            })




                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            console.log(j, arr)
            let find = Object.values(j).filter(i => (i['a1'] !== undefined));

            Object.values(j).forEach((k) => {
                console.log(k)
                if (k.category !== null && find.length) {
                    let name1 = 'NA'
                    if (k['a1'] || k['a1'] === 0) {
                        name1 = land_veh_list[0]
                    } else if (k['b1'] || k['b1'] === 0) {
                        console.log('xyz')
                        name1 = land_veh_list[1]
                    } else if (k['c1'] || k['c1'] === 0) {
                        name1 = land_veh_list[2]
                    } else if (k['d1'] || k['d1'] === 0) {
                        name1 = land_veh_list[3]
                    }
                    else if (k['e1'] || k['e1'] === 0) {
                        name1 = land_veh_list[4]
                    }
                    else if (k['f1'] || k['f1'] === 0) {
                        name1 = land_veh_list[5]
                    }


                    k.dp = (name1 === undefined ? 'NA' : name1 + ' ^ ' + unit)



                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    const getValueByMonthlyEmpCat275 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]
        let land_veh_list = ['Standard Taxi / Sedan', 'Hybrid Vehicle', 'Executive Taxi', 'Dual Purpose 4 x 4 (SUV)', 'Electric Vehicle (EV)', 'Van/ Coach']
        const value_arr = ['a1', 'b1', 'c1', 'd1', 'e1', 'f1']
        let final_result = {};

        console.log(tier0, tier1, tier2, tier3)
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                console.log(getRPTextFormat(i.reporting_period) === obj, dcfId === i.dcfId, i.tier0_id === tier0,
                    i.tier1_id === tier1,
                    i.tier2_id === tier2,
                    i.tier3_id === tier3, i.id, i.tier3_id, tier3);
                return (
                    i.dcfId === dcfId &&
                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                console.log(index2, obj)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let abc = {}

                        abc['a1'] = i['DPAN613'];
                        abc['b1'] = i['DPAN614'];
                        abc['c1'] = i['DPAN615'];
                        abc['d1'] = i['DPAN616'];
                        abc['e1'] = i['DPAN617'];
                        abc['f1'] = i['DPAN618'];
                        value_arr.forEach((x) => {
                            list_arr2.push({
                                ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                col: getNumberOfColumn(obj),
                                id: qn_submit_list[index].id,
                                category: 1,
                                form_type: 2,
                                diff: false,
                                current: abc[x],
                                month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                percentage: '-',
                                type, self,
                                text:
                                    type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? "Unlocked for Resubmission"
                                            : type === 1 && reject === 2
                                                ? "Unlocked for Review Again"
                                                : type === 1
                                                    ? "Pending Review"
                                                    : type === 2
                                                        ? "Pending Approval"
                                                        : type === 3
                                                            ? "Approved & Locked"
                                                            : "Pending Submission",
                                status:
                                    type === 0 && (reject === 0 || reject === null) ? 0 :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? 1
                                            : type === 1 && reject === 2
                                                ? 2
                                                : type === 1
                                                    ? 3
                                                    : type === 2
                                                        ? 4
                                                        : type === 3
                                                            ? 5
                                                            : null
                            })
                        })


                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let abc = {}
                        abc['a1'] = i['DPAN613'];
                        abc['b1'] = i['DPAN614'];
                        abc['c1'] = i['DPAN615'];
                        abc['d1'] = i['DPAN616'];
                        abc['e1'] = i['DPAN617'];
                        abc['f1'] = i['DPAN618'];

                        value_arr.forEach((x) => {
                            console.log(list_arr2)
                            let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]

                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(abc[x], value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })

                            } else {

                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '100%',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }
                        })





                    })


                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {

                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    console.log(qn_submit_list[index])
                    if (qn_submit_list[index].response.length) {

                        qn_submit_list[index].response.forEach((i) => {
                            let abc = {}

                            abc['a1'] = i['DPAN613'];
                            abc['b1'] = i['DPAN614'];
                            abc['c1'] = i['DPAN615'];
                            abc['d1'] = i['DPAN616'];
                            abc['e1'] = i['DPAN617'];
                            abc['f1'] = i['DPAN618'];
                            value_arr.forEach((x) => {
                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    form_type: 2,
                                    category: 1,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: "-",
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            })




                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            console.log(j, arr)
            let find = Object.values(j).filter(i => (i['a1'] !== undefined));

            Object.values(j).forEach((k) => {
                console.log(k)
                if (k.category !== null && find.length) {
                    let name1 = 'NA'
                    if (k['a1'] || k['a1'] === 0) {
                        name1 = 'Senior Management (Male)'
                    } else if (k['b1'] || k['b1'] === 0) {
                        console.log('xyz')
                        name1 = 'Senior Management (Female)'
                    } else if (k['c1'] || k['c1'] === 0) {
                        name1 = 'Middle Management (Male)'
                    } else if (k['d1'] || k['d1'] === 0) {
                        name1 = 'Middle Management (Female)'
                    }
                    else if (k['e1'] || k['e1'] === 0) {
                        name1 = 'Non-Management (Male)'
                    }
                    else if (k['f1'] || k['f1'] === 0) {
                        name1 = 'Non-Management (Female)'
                    }


                    k.dp = (name1 === undefined ? 'NA' : name1 + ' ^ ' + unit)



                } else {
                    k.dp = null
                }
            })
        })
        console.log(final_result)
        return final_result;
    };
    const getValueByMonthlyEmpNewTurn284 = (
        dcfId,
        tier0,
        tier1,
        tier2,
        tier3,
        arr, qn_submit_list, unit, self, refobj
    ) => {
        // let months = ['Jan-' + yr, 'Feb-' + yr, 'Mar-' + yr, 'Apr-' + yr, 'May-' + yr, 'Jun-' + yr, 'Jul-' + yr, 'Aug-' + yr, 'Sep-' + yr, 'Oct-' + yr, 'Nov-' + yr, 'Dec-' + yr]
        let land_veh_list = ['Standard Taxi / Sedan', 'Hybrid Vehicle', 'Executive Taxi', 'Dual Purpose 4 x 4 (SUV)', 'Electric Vehicle (EV)', 'Van/ Coach']
        const value_arr = ['a1', 'b1', 'c1', 'd1']
        let final_result = {};

        console.log(tier0, tier1, tier2, tier3)
        arr.forEach((obj) => {
            console.log(obj)
            let result = {
                [obj]: [refobj],
            };
            let result_arr = [];
            let index = qn_submit_list.findIndex((i) => {
                console.log(getRPTextFormat(i.reporting_period) === obj, dcfId === i.dcfId, i.tier0_id === tier0,
                    i.tier1_id === tier1,
                    i.tier2_id === tier2,
                    i.tier3_id === tier3, i.id, i.tier3_id, tier3);
                return (
                    i.dcfId === dcfId &&
                    i.tier0_id === tier0 &&
                    i.tier1_id === tier1 &&
                    i.tier2_id === tier2 &&
                    i.tier3_id === tier3 &&
                    getRPTextFormat(i.reporting_period) === obj
                );
            });
            
            if (index !== -1) {
                let index2 = qn_submit_list.findIndex((i) => {
                    console.log(getPreviousPeriod(obj.includes("to") ? 2 : 1, obj), getRPTextFormat(
                        i.reporting_period[i.reporting_period.length - 1]
                    ), obj)
                    return (
                        i.dcfId === dcfId &&
                        (i.type === 0 ? i.reject === 1 : true) &&
                        i.tier0_id === tier0 &&
                        i.tier1_id === tier1 &&
                        i.tier2_id === tier2 &&
                        i.tier3_id === tier3 &&
                        getRPTextFormat(
                            [i.reporting_period[i.reporting_period.length - 1]]
                        ) === getPreviousPeriod(obj.includes("to") ? 2 : 1, obj)
                    );
                });
                console.log(index2, obj)
                if (index2 !== -1 && qn_submit_list[index2].response.length && qn_submit_list[index].response.length) {
                    const { type1, reject1 } = qn_submit_list[index2];
                    const { type, reject } = qn_submit_list[index];
                    let value = null,
                        value2 = null;
                    result_arr = [];
                    let list_arr = []
                    let list_arr2 = []
                    qn_submit_list[index2].response.forEach((i) => {
                        let abc = {}

                        abc['a1'] = i['DPAN641'] + i['DPAN642'] + i['DPAN643'];
                        abc['b1'] = i['DPAN644'] + i['DPAN645'] + i['DPAN646'];
                        abc['c1'] = i['DPAN672'] + i['DPAN673'] + i['DPAN674']
                        abc['d1'] = i['DPAN675'] + i['DPAN676'] + i['DPAN677'];

                        value_arr.forEach((x) => {
                            list_arr2.push({
                                ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                col: getNumberOfColumn(obj),
                                id: qn_submit_list[index].id,
                                category: 1,
                                form_type: 2,
                                diff: false,
                                current: abc[x],
                                month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                percentage: '-',
                                type, self,
                                text:
                                    type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? "Unlocked for Resubmission"
                                            : type === 1 && reject === 2
                                                ? "Unlocked for Review Again"
                                                : type === 1
                                                    ? "Pending Review"
                                                    : type === 2
                                                        ? "Pending Approval"
                                                        : type === 3
                                                            ? "Approved & Locked"
                                                            : "Pending Submission",
                                status:
                                    type === 0 && (reject === 0 || reject === null) ? 0 :
                                        (type === 0 && (reject === 1 || reject === 2))
                                            ? 1
                                            : type === 1 && reject === 2
                                                ? 2
                                                : type === 1
                                                    ? 3
                                                    : type === 2
                                                        ? 4
                                                        : type === 3
                                                            ? 5
                                                            : null
                            })
                        })


                    })
                    qn_submit_list[index].response.forEach((i) => {
                        let abc = {}
                        abc['a1'] = i['DPAN641'] + i['DPAN642'] + i['DPAN643'];
                        abc['b1'] = i['DPAN644'] + i['DPAN645'] + i['DPAN646'];
                        abc['c1'] = i['DPAN672'] + i['DPAN673'] + i['DPAN674']
                        abc['d1'] = i['DPAN675'] + i['DPAN676'] + i['DPAN677'];

                        value_arr.forEach((x) => {
                            console.log(list_arr2)
                            let oldIndex = list_arr2.findIndex((j) => j[x] !== undefined)
                            if (oldIndex !== -1) {
                                value2 = list_arr2[oldIndex][x]

                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: abc[x] === value2 ? null : abc[x] > value2 ? false : true,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: calculatePercentage(abc[x], value2),
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })

                            } else {

                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    category: 2,
                                    form_type: 2,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: '100%',
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            }
                        })





                    })


                    result = { [obj]: attachObj(list_arr, refobj) };
                } else {

                    const { type, reject } = qn_submit_list[index];
                    let value = null;
                    result_arr = [];
                    let list_arr = []
                    console.log(qn_submit_list[index])
                    if (qn_submit_list[index].response.length) {

                        qn_submit_list[index].response.forEach((i) => {
                            let abc = {}

                            abc['a1'] = i['DPAN641'] + i['DPAN642'] + i['DPAN643'];
                            abc['b1'] = i['DPAN644'] + i['DPAN645'] + i['DPAN646'];
                            abc['c1'] = i['DPAN672'] + i['DPAN673'] + i['DPAN674']
                            abc['d1'] = i['DPAN675'] + i['DPAN676'] + i['DPAN677'];
                            value_arr.forEach((x) => {
                                list_arr.push({
                                    ...i, [x]: abc[x], remarks: qn_submit_list[index].return_remarks,
                                    col: getNumberOfColumn(obj),
                                    id: qn_submit_list[index].id,
                                    form_type: 2,
                                    category: 1,
                                    diff: false,
                                    current: abc[x],
                                    month: getRPTextFormat(qn_submit_list[index].reporting_period),
                                    percentage: "-",
                                    type, self,
                                    text:
                                        type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? "Unlocked for Resubmission"
                                                : type === 1 && reject === 2
                                                    ? "Unlocked for Review Again"
                                                    : type === 1
                                                        ? "Pending Review"
                                                        : type === 2
                                                            ? "Pending Approval"
                                                            : type === 3
                                                                ? "Approved & Locked"
                                                                : "Pending Submission",
                                    status:
                                        type === 0 && (reject === 0 || reject === null) ? 0 :
                                            (type === 0 && (reject === 1 || reject === 2))
                                                ? 1
                                                : type === 1 && reject === 2
                                                    ? 2
                                                    : type === 1
                                                        ? 3
                                                        : type === 2
                                                            ? 4
                                                            : type === 3
                                                                ? 5
                                                                : null
                                })
                            })




                        })
                    }
                    else {
                        list_arr.push({
                            edit: 1,
                            remarks: qn_submit_list[index].return_remarks,
                            col: getNumberOfColumn(obj),
                            id: qn_submit_list[index].id,
                            form_type: 2,
                            category: 1,
                            diff: false,
                            current: '-',
                            month: getRPTextFormat(qn_submit_list[index].reporting_period),
                            percentage: "-",
                            type, self,
                            text:
                                type === 0 && (reject === 0 || reject === null) ? "Drafted" :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? "Unlocked for Resubmission"
                                        : type === 1 && reject === 2
                                            ? "Unlocked for Review Again"
                                            : type === 1
                                                ? "Pending Review"
                                                : type === 2
                                                    ? "Pending Approval"
                                                    : type === 3
                                                        ? "Approved & Locked"
                                                        : "Pending Submission",
                            status:
                                type === 0 && (reject === 0 || reject === null) ? 0 :
                                    (type === 0 && (reject === 1 || reject === 2))
                                        ? 1
                                        : type === 1 && reject === 2
                                            ? 2
                                            : type === 1
                                                ? 3
                                                : type === 2
                                                    ? 4
                                                    : type === 3
                                                        ? 5
                                                        : null
                        })
                    }



                    result = { [obj]: attachObj(list_arr, refobj) };
                }
            }

            final_result = { ...final_result, ...result };
        });

        Object.values(final_result).forEach((j) => {
            console.log(j, arr)
            let find = Object.values(j).filter(i => (i['a1'] !== undefined));

            Object.values(j).forEach((k) => {
                console.log(k)
                if (k.category !== null && find.length) {
                    let name1 = 'NA'
                    if (k['a1'] || k['a1'] === 0) {
                        name1 = 'Total Number of Hires (Male)'
                    } else if (k['b1'] || k['b1'] === 0) {
                        console.log('xyz')
                        name1 = 'Total Number of Hires (Female)'
                    } else if (k['c1'] || k['c1'] === 0) {
                        name1 = 'Total Employee Turnover (Male)'
                    } else if (k['d1'] || k['d1'] === 0) {
                        name1 = 'Total Employee Turnover (Female)'
                    }


                    k.dp = (name1 === undefined ? 'NA' : name1 + ' ^ ' + unit)



                } else {
                    k.dp = null
                }
            })
        })
        // Object.values(final_result).forEach((y)=>{
        //     y.splice(1, 0, {dp:'Male'})
        //     y.splice(5, 0, {dp:'Female'})
        // })
        console.log(final_result)
        return final_result;
    };
    function arraysAreEqual(arr1, arr2) {
        // Check if the arrays have the same length
        if (arr1.length !== arr2.length) {
            return false;
        }

        // Check if each element in arr1 matches the corresponding element in arr2
        for (let i = 0; i < arr1.length; i++) {
            if (arr1[i] !== arr2[i]) {
                return false;
            }
        }

        // If all elements match and lengths are the same, arrays are equal
        return true;
    }
    function getUniqueYearsArray(array1, array2) {
        // Convert arrays to sets to ensure uniqueness
        const set1 = new Set(array1);
        const set2 = new Set(array2);

        // Merge the sets
        const mergedSet = new Set([...set1, ...set2]);

        // Convert the merged set back to an array
        const uniqueYearsArray = Array.from(mergedSet);

        return uniqueYearsArray;
    }

    function getYearsArray(start_date, end_date = null) {
        // Parse the start date
        const startDate = DateTime.fromISO(start_date);

        // Parse the end date or use current UTC local date if end_date is null
        const endDate = end_date ? DateTime.fromISO(end_date) : DateTime.utc();

        // Get the years between start and end dates
        const yearsArray = [];
        for (let year = startDate.year; year <= endDate.year; year++) {
            console.log(year)
            yearsArray.push(year);

        }

        return yearsArray;
    }
    const checkEnity = (rowData, entity_list, rawsite, obj) => {
        let index = entity_list.findIndex((k) => k[obj] === rowData[obj]);
        if (index !== -1) {
            let entity = entity_list[index];
            
            if (rowData.level === 0) {
                return entity.tier0_ids.includes(0);
            } else if (rowData.level === 1) {
                return (
                    entity.tier1_ids.includes(rowData.locationId) &&
                    getCoverageText(rowData, rawsite)
                );
            } else if (rowData.level === 2) {
                return (
                    entity.tier2_ids.includes(rowData.locationId) &&
                    getCoverageText(rowData, rawsite)
                );
            } else if (rowData.level === 3) {
                return (
                    entity.tier3_ids.includes(rowData.locationId) &&
                    getCoverageText(rowData, rawsite)
                );
            }
        } else {
            return false;
        }
    };
    const getDataPointUnit = (rowData, dcfId) => {
        console.log(rowData)
        let text = "Not Found";
        let filteredList = dplist
            .filter(
                (i) =>
                    i.suffix !== null &&
                    i.suffix.trim().toLowerCase() === rowData.name.trim().toLowerCase()
            )
            .findIndex(
                (i) => Array.isArray(i.data1)
            );
        console.log(rowData.dcfId, dplist
            .filter(
                (i) =>
                    i.suffix !== null &&
                    i.suffix.trim().toLowerCase() === rowData.name.trim().toLowerCase()
            ))
        if (filteredList !== -1) {
            text = dplist.filter(
                (i) =>
                    i.suffix !== null &&
                    i.suffix.trim().toLowerCase() === rowData.name.trim().toLowerCase()
            )[filteredList].data1[0].unit;
        }
        
        return text;
    };
    const getCoverageText = (rowData, rawsitelist) => {
        let text = "";
        
        if (rowData.level === 0) {
            text = "Corporate";
        } else if (rowData.level === 1) {
            let country_index = rawsitelist.findIndex(
                (i) => i.id === rowData.locationId
            );
            if (country_index !== -1) {
                text = rawsitelist[country_index].name;
            }
        } else if (rowData.level === 2) {
            let city_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.city_id === rowData.locationId;
                });
            if (city_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[city_index].city_name;
            }
        } else if (rowData.level === 3) {
            let site_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.site_id === rowData.locationId;
                });
            if (site_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[site_index].site_name;
            }
        }
        return text;
    };
    function getNumberOfColumn(monthString) {
        if (monthString.includes("to")) {
            const [startMonth, endMonth] = monthString.split(" to ");

            const startDate = DateTime.fromFormat(startMonth, "LLL-yyyy");
            let endDate = DateTime.local(); // Default to current month and year
            if (endMonth) {
                endDate = DateTime.fromFormat(endMonth, "LLL-yyyy");
            }

            // Calculate the difference in months
            const diffMonths = endDate.diff(startDate, "months").months + 1;

            return diffMonths;
        } else {
            return 1;
        }
    }

    const getCoverageTextById = (rowData, rawsitelist) => {
        let text = "";
        
        if (rowData.tier0_id !== null) {
            text = "Corporate";
        } else if (rowData.tier1_id !== null) {
            let country_index = rawsitelist.findIndex(
                (i) => i.id === rowData.tier1_id
            );
            if (country_index !== -1) {
                text = rawsitelist[country_index].name;
            }
        } else if (rowData.tier2_id !== null) {
            let city_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.city_id === rowData.tier2_id;
                });
            if (city_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[city_index].city_name;
            }
        } else if (rowData.tier3_id !== null) {
            let site_index = rawsitelist
                .flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )
                .findIndex((i) => {
                    return i.site_id === rowData.tier3_id;
                });
            if (site_index !== -1) {
                text = rawsitelist.flatMap((i) =>
                    i.locationTwos.flatMap((j) =>
                        j.locationThrees.map((k) => {
                            return {
                                site_id: k.id,
                                site_name: k.name,
                                city_id: j.id,
                                city_name: j.name,
                                country_id: i.id,
                                country_name: i.name,
                            };
                        })
                    )
                )[site_index].site_name;
            }
        }
        return text;
    };
    const titleTemplate = (rowData) => {
        let text = "Not Found";
        let dcf_index = dcflist.findIndex((i) => i.id === rowData.dcfId);
        if (dcf_index !== -1) {
            text = dcflist[dcf_index].title;
        }
        return (
            <div
                className="text-underline cur-pointer clr-navy fw-5"
                onClick={() => {
                    navigate.push({
                        pathname:
                            "/data_input_reviewer/" + rowData.dcfId + "/" + rowData.id,
                    });
                }}
            >
                {text}
            </div>
        );
    };
    const getFormTitle = (rowData) => {
        let text = "Not Found";
        let dcf_index = dcflist.findIndex((i) => i.id === rowData.dcfId);
        if (dcf_index !== -1) {
            text = dcflist[dcf_index].title;
        }
        return text;
    };
    const dpNameTemplate = (rowData) => {
        console.log(rowData)
        return (
            <>
                {rowData.label
                    .replace(/(<([^>]+)>)/gi, "")
                    .replace(/\n/g, " ")
                    .replace(/&nbsp;/g, " ")
                    .replace("&amp;", "&").split('^')[0]}
            </>
        );
    };
    const coverageTemplate = (rowData) => {
        return <>{getCoverageText(rowData, rawsitelist)}</>;
    };
    const getUser = (id) => {
        let user_name = 'Not Found'
        let index = userList.findIndex(i => i.id === id)
        if (index !== -1) {
            user_name = userList[index].information.empname
        }
        return user_name
    };
    const submitterTemplate = (rowData) => {
        return <>{getUser(rowData.reporter_modified_by)}</>;
    };
    const submittedTemplate = (rowData) => {
        return (
            <>
                {DateTime.fromISO(rowData.last_modified_on, { zone: "utc" })
                    .toLocal()
                    .toFormat("dd-LLL-yyyy HH:mm")}
            </>
        );
    };
    const rpFrequencyTemplate = (rowData) => {
        return <span>{getFrequencyText(rowData.frequency)}</span>;
    };
    const janTemplate = (rowData) => {
        return <span>{rowData.locationId}</span>;
    };
    const getColumn = (rowData) => {
        
        return 1;
    };

    const getFrequencyText = (id) => {
        return frequency_list.find((i) => {
            return i.id === id;
        }).name;
    };
    const sortRP = (e) => {
        
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromFormat(a.reporting_period, "MMM-yyyy");
                let dateB = DateTime.fromFormat(b.reporting_period, "MMM-yyyy");
                if (a.reporting_period.includes("to")) {
                    dateA = DateTime.fromFormat(
                        a.reporting_period.split("to")[0].trim(),
                        "MMM-yyyy"
                    );
                }
                if (b.reporting_period.includes("to")) {
                    dateB = DateTime.fromFormat(
                        b.reporting_period.split("to")[0].trim(),
                        "MMM-yyyy"
                    );
                }

                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                let dateA = DateTime.fromFormat(a.reporting_period, "MMM-yyyy");
                let dateB = DateTime.fromFormat(b.reporting_period, "MMM-yyyy");
                if (a.reporting_period.includes("to")) {
                    dateA = DateTime.fromFormat(
                        a.reporting_period.split("to")[0].trim(),
                        "MMM-yyyy"
                    );
                }
                if (b.reporting_period.includes("to")) {
                    dateB = DateTime.fromFormat(
                        b.reporting_period.split("to")[0].trim(),
                        "MMM-yyyy"
                    );
                }
                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };
    const getStatusText = (val, rowData) => {
        let text = "NOT SUBMITTED";
        
        if (rowData !== undefined) {
            if (rowData.type === 1) {
                text = "Not Reviewed";
            } else if (rowData.type === 2) {
                if (rowData.self) {
                    text = "Self Reviewed";
                } else {
                    text = "Reviewed";
                }
            } else if (rowData.type === 3) {
                text = "APPROVED";
            }
        }
        return text;
    };
    const sortStatus_ = (e) => {
        if (e.order === 1) {
            return e.data.sort((a, b) => {
                const dateA = getStatusText("", a);
                const dateB = getStatusText("", b);
                
                // Compare the parsed dates
                if (dateA < dateB) return -1;
                if (dateA > dateB) return 1;
                return 0;
            });
        } else {
            return e.data.sort((a, b) => {
                const dateA = getStatusText("", a);
                const dateB = getStatusText("", b);

                // Compare the parsed dates in descending order
                if (dateA > dateB) return -1;
                if (dateA < dateB) return 1;
                return 0;
            });
        }
    };
    const statusTemplate = (rowData) => {
        let type = 0;
        let text = "NOT SUBMITTED";
        let classtext = "status-tag-gray";
        if (rowData !== undefined) {
            if (rowData.type === 0) {
                if (rowData.reject === 1) {
                    type = 1;
                    text = "RETURNED";
                    classtext = "status-tag-red";
                } else {
                    type = 2;
                    text = "DRAFT";
                    classtext = "status-tag-orange";
                }
            } else if (rowData.type === 1) {
                if (rowData.reject === 1) {
                    type = 1;
                    text = "RETURNED";
                    classtext = "status-tag-red";
                } else {
                    type = 3;
                    text = "SUBMITTED";
                    classtext = "status-tag-green";
                }
            } else if (rowData.type === 2) {
                type = 3;
                if (rowData.self) {
                    text = "Self Reviewed";
                } else {
                    text = "Reviewed";
                }

                classtext = "status-tag-green";
            } else if (rowData.type === 3) {
                type = 4;
                text = "APPROVED";
                classtext = "status-tag-green";
            }
        }
        return (
            <Tag style={{ padding: "4px 8px" }} className={classtext}>
                {text}
            </Tag>
        );
    };
    const extractDPFromForms = (list) => {
        let form = JSON.parse(JSON.stringify(list));

        const updatedResponses = form.flatMap((submission) => {
            return submission.response
                .filter((response) => response.type === "number")
                .map((response) => ({
                    ...response,
                    colSpan: submission.reporting_period.length,
                    ...submission,
                }));
        });

        // setRefinedData(updatedResponses)
    };


    const updateSelectedItem = (item, index) => {
        let list_loc = JSON.parse(JSON.stringify(refineddata));
        list_loc.forEach((item, j) => {
            if (index === j) {
                item.expanded = item.expanded === undefined ? true : !item.expanded;
            }
        });
        setRefinedData(list_loc);
        
        forceUpdate();
    };
    function filterAndSortByYear(data, year) {
        const keys = Object.keys(data)
            .filter((key) => key.includes(year))
            .sort((a, b) => {
                const [monthA] = a.split("-");
                const [monthB] = b.split("-");
                return (
                    DateTime.fromFormat(monthA, "LLL").month -
                    DateTime.fromFormat(monthB, "LLL").month
                );
            });

        return keys.reduce((result, key) => {
            result[key] = data[key];
            return result;
        }, {});
    }
    const getMonthKey = (key) => {
        if (key.includes("to")) {
            const [from, to] = key.split(" to ");

            return from.split("-")[0] + " to " + to.split("-")[0];
        } else {
            return key.split("-")[0];
        }
    };
    const updateRefindedDataByFilter = (val) => {
        let loc = JSON.parse(JSON.stringify(refineddatabk));
        let data_ = []
        if (val) {

            setFilter((prev) => ({ ...prev, [val]: [] }));
            if (val === 'entity') {
                data_ = loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) })
                setRefinedData(loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) }));
            } else if (val === 'form') {
                data_ = loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) })
                setRefinedData(loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) }));
            } else {
                data_ = loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) })
                setRefinedData(loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) }));
            }
            let statusCounter = { '0': 0, '1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0, '7': 0, '8': 0 }
            data_.forEach(i => {
                console.log(i)
                Object.keys(statusCounter).forEach((key) => {
                    statusCounter[key] = statusCounter[key] + i.statusArray.filter(i => i === parseFloat(key)).length
                })

            })
            setStatusCodes(statusCounter)
        } else {
            setRefinedData(loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) }));
            data_ = loc.filter((i) => { return (i.year === filter.year || filter.year === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) })
            let statusCounter = { '0': 0, '1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0, '7': 0, '8': 0 }
            data_.forEach(i => {
                console.log(i)
                Object.keys(statusCounter).forEach((key) => {
                    statusCounter[key] = statusCounter[key] + i.statusArray.filter(i => i === parseFloat(key)).length
                })

            })
            setStatusCodes(statusCounter)
        }

        setFirst(0);

    }
    const onFilterChange = (obj, val) => {

        setFilter((prev) => ({ ...prev, [obj]: val }));
    };
    const onPageChange = (event) => {
        console.log(event)
        setFirst(event.first);
        setRows(event.rows);
    };
    const onSelectedChangeIntial = (obj, val, refloc) => {
        let loc = JSON.parse(JSON.stringify(refloc));
        console.log(val)
        if (obj === 'year') {
            let data_ = loc.filter((i) => { return (i.year === val || val === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) })
            let statusCounter = { '0': 0, '1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0, '7': 0, '8': 0 }
            data_.forEach(i => {
                console.log(i)
                Object.keys(statusCounter).forEach((key) => {
                    statusCounter[key] = statusCounter[key] + i.statusArray.filter(i => i === parseFloat(key)).length
                })

            })
            setStatusCodes(statusCounter)
            setRefinedData(data_);
        }


        setLoading(true);

        setFilter((prev) => ({ ...prev, [obj]: val }));
    };
    const onSelectedChange = (obj, val) => {
        let loc = JSON.parse(JSON.stringify(refineddatabk));
        let qnloc = JSON.parse(JSON.stringify(quantitativesubmission))
        console.log(val)
        if (obj === 'year') {
            calculateAndCreateYearData(assignedlist, dcflist, qnloc, stdlist, { cat1: subcat1, cat2: subcat2, cat3: subcat3, cat4: subcat4 }, val).then((res) => {
                setRefinedDataBk(res)
                console.log(res)
                let data_ = res.filter((i) => { return (i.year === val || val === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) })
                let statusCounter = { '0': 0, '1': 0, '2': 0, '3': 0, '4': 0, '5': 0, '6': 0, '7': 0, '8': 0 }
                data_.forEach(i => {
                    console.log(i)
                    Object.keys(statusCounter).forEach((key) => {
                        statusCounter[key] = statusCounter[key] + i.statusArray.filter(i => i === parseFloat(key)).length
                    })

                })
                setStatusCodes(statusCounter)
                let entity_option = Array.from(new Set(res.map(i => getCoverageTextById(i, rawsitelist)))).map(i => ({ title: i, id: i }))
                setFormOption(dcflist.filter(i => res.map(i => i.dcfId).includes(i.id)))
                setEntityOption(entity_option)
                setRefinedData(res.filter((i) => { return (i.year === val || val === null) && (filter.entity.includes(getCoverageTextById(i, rawsitelist)) || filter.entity.length === 0) && (filter.form.includes(i.dcfId) || filter.form.length === 0) && (i.status.some(status => filter.status.includes(status)) || filter.status.length === 0) }));

            }

            )

        }
        setFirst(0)
        setRows(20)
        setFilter((prev) => ({ ...prev, [obj]: val }));
    };
    const getRPLuxon = (months) => {
        if (months.includes('to')) {
            let startDate = DateTime.fromFormat(months.split('to')[0].trim(), 'LLL-yyyy')
            let endDate = DateTime.fromFormat(months.split('to')[1].trim(), 'LLL-yyyy')
            let rp = []
            while (startDate <= endDate) {
                rp.push(startDate.toFormat('LL-yyyy'));
                startDate = startDate.plus({ months: 1 })
            }
            return rp
        } else {
            return [DateTime.fromFormat(months, 'LLL-yyyy').toFormat('LL-yyyy')]
        }
    }
    const sortStatus = (a, b) => {
        if (filter.status === 0) {
            return a.status - b.status
        }
        else if (filter.status === 1) {
            return b.status - a.status
        } else {
            console.log('sort')
            return true
        }
    }
    const getRoles = (info) => {
        if (info.role !== undefined || info.role !== null) {
            return Object.entries(info.role).filter(i => i[1] === true).map(i => i[0]).join('/ ')
        } else {
            return 'Unknown'
        }
    }

    return (
        <div className="bg-smoke font-lato">
            <div className="col-12">
                <div>

                    <div
                        className="col-12 "
                        style={{
                            justifyContent: "center",
                        }}
                    >
                        <label className="text-big-one clr-navy flex fs-16 flex justify-content-start">

                            Quantitative Submission Status
                        </label>
                    </div>
                    <div className="col-12 grid">
                        <div className="col-6 grid align-items-center">
                            <div className="col-3">
                                <label className="fs-16 fw-5">Select Year</label>
                            </div>
                            <div className="col-8">
                                <Dropdown
                                    optionLabel="title"
                                    optionValue="value"
                                    style={{ width: 200 }}
                                    options={yearoption.sort((a, b) => { return a.value - b.value }, 0)}
                                    value={filter.year}
                                    onChange={(e) => {
                                        onSelectedChange("year", e.value);
                                    }}
                                />
                            </div>
                        </div>
                    </div>
                    {/* {refineddata.filter((x) => x.dp_array.length > 1).length !== 0 && <div className=" grid justify-content-between" style={{ padding: 0,margin:0,marginBottom:5 }}>

                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Resubmission</div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[1]}</div>  </div>
                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Submission Overdue </div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[2]}</div>  </div>
                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Submission Due </div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[3]}</div>  </div>
                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Pending Review </div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[5]}</div>  </div>
                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Pending Approval </div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[6]}</div>  </div>
                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Approved </div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[7]}</div>  </div>
                        <div className="col-2 grid" style={{
                            padding: '0px', margin: 5,
                            display: 'flex',
                            flexDirection: 'row'
                        }}><div className=" fs-24 fw-7 stt-status-display-card" >Upcoming </div>  <div className="clr-stt fs-24 fw-7 stt-status-count-card" > {statusCodes[4]}</div>  </div>

                    </div>} */}

                    {load ? <div >
                        <div
                            className="col-12 flex justify-content-between"
                            style={{ flexDirection: "column", background: "#31597530" }}
                        >
                            <div className="col-11 flex" style={{ flexDirection: "row" }}>
                                <div className="col-2">
                                    <div className="grid m-0" >
                                        <div className=" fw-7 fs-16">Reporting Entity    </div>
                                        <div className="ml-1" > <i style={{ background: filter.entity.length !== 0 && 'aliceblue' }} className="material-icons cur-pointer" onClick={(e) => entityref.current.toggle(e)} >{filter.entity.length !== 0 ? 'filter_alt_off' : 'filter_alt'}</i> </div>
                                        <OverlayPanel ref={entityref} style={{ width: 250 }} >

                                            <MultiSelect value={filter.entity} onChange={(e) => onFilterChange('entity', e.value)} options={entityoption} optionLabel="title" optionValue="id"
                                                filterBy="title" filter maxSelectedLabels={3} placeholder="Select Entity" className="w-full md" panelClassName={'hidefilter'} />

                                            <div className="col-12 mt-2 flex justify-content-between ">
                                                <div >
                                                    <Button label='Clear' outlined onClick={() => { updateRefindedDataByFilter('entity') }} />
                                                </div>
                                                <div >
                                                    <Button label='Apply' disabled={filter.entity.length === 0} onClick={() => { updateRefindedDataByFilter() }} />
                                                </div>
                                            </div>
                                        </OverlayPanel>
                                    </div>
                                </div>
                                <div className="col-8">
                                    <div className="grid m-0">
                                        <div className=" fw-7 fs-16">Form </div>
                                        <div className="ml-1" > <i style={{ background: filter.form.length !== 0 && 'aliceblue' }} className="material-icons cur-pointer" onClick={(e) => formref.current.toggle(e)} >{filter.form.length !== 0 ? 'filter_alt_off' : 'filter_alt'}</i> </div>
                                        <OverlayPanel ref={formref} style={{ width: 250 }} >

                                            <MultiSelect value={filter.form} onChange={(e) => onFilterChange('form', e.value)} options={formoption} optionLabel="title" optionValue="id"
                                                filterBy="title" filter maxSelectedLabels={3} placeholder="Select Form" className="w-full md" panelClassName={'hidefilter'} />

                                            <div className="col-12 mt-2 flex justify-content-between ">
                                                <div >
                                                    <Button label='Clear' outlined onClick={() => { updateRefindedDataByFilter('form') }} />
                                                </div>
                                                <div >
                                                    <Button label='Apply' disabled={filter.form.length === 0} onClick={() => { updateRefindedDataByFilter() }} />
                                                </div>
                                            </div>
                                        </OverlayPanel>
                                    </div>
                                </div>
                                <div className="col-2">
                                    <div className="grid m-0">
                                        <div className=" fw-7 fs-16" >Submission Status </div>
                                        <div className="ml-1" > <i style={{ background: filter.status.length !== 0 && 'aliceblue' }} className="material-icons cur-pointer" onClick={(e) => statusref.current.toggle(e)} >{filter.status.length !== 0 ? 'filter_alt_off' : 'filter_alt'}</i> </div>
                                        <OverlayPanel ref={statusref} style={{ width: 250 }} >

                                            <MultiSelect value={filter.status} onChange={(e) => onFilterChange('status', e.value)} options={[{ title: 'Resubmission', id: 1 }, { title: 'Review Again', id: 8 }, { title: 'Submission Overdue', id: 2 }, { title: 'Submission Due', id: 3 }, { title: 'Pending Review', id: 5 }, { title: 'Pending Approval', id: 6 }, { title: 'Approved', id: 7 }, { title: 'Upcoming', id: 4 }]} optionLabel="title" optionValue="id"
                                                placeholder="Select Status" className="w-full md" panelClassName={'hidefilter'} />

                                            <div className="col-12 mt-2 flex justify-content-between ">
                                                <div >
                                                    <Button label='Clear' outlined onClick={() => { updateRefindedDataByFilter('status') }} />
                                                </div>
                                                <div >
                                                    <Button label='Apply' disabled={filter.status.length === 0} onClick={() => { updateRefindedDataByFilter() }} />
                                                </div>
                                            </div>
                                        </OverlayPanel>
                                    </div>
                                </div>
                            </div>
                            {refineddata.filter((x) => x.dp_array.length > 1).length !== 0 && <div className="col-12 grid justify-content-between" style={{ padding: 0, margin: 0, marginBottom: 5 }}>


                                <div className="col-2 flex justify-content-center" style={{ border: '1px solid gray' }}> <span className='mr-1'>{statusCodes[7]}</span> Approved </div>
                                <div className="col-2 flex justify-content-center" style={{ border: '1px solid gray' }}> <span className='mr-1'>{statusCodes[6]}</span> Pending Approval </div>
                                <div className="col-2 flex justify-content-center" style={{ border: '1px solid gray' }}> <span className='mr-1'>{statusCodes[5]}</span> Pending Review </div>
                                <div className="col-2 flex justify-content-center" style={{ border: '1px solid gray' }}> <span className='mr-1'>{statusCodes[1]}</span> Resubmission Required</div>
                                <div className="col-2 flex justify-content-center" style={{ border: '1px solid gray' }}><span className='mr-1'>{statusCodes[3]}</span> Submission Due  </div>
                                <div className="col-2 flex justify-content-center" style={{ border: '1px solid gray' }}> <span className='mr-1'>{statusCodes[2]}</span>  Submission Overdue </div>
                            </div>}
                        </div>
                        {refineddata.filter((x) => x.dp_array.length > 1).length ?
                            <BlockUI blocked={tempload}>
                                <div style={{
                                    height: 'calc(100vh - 505px)',
                                    overflow: 'auto'
                                }}>

                                    {
                                        refineddata.filter((x) => x.dp_array.length > 1).slice(first, first + rows).map((item, index) => {
                                            console.log(item)
                                            return (
                                                <div >
                                                    <div className="m-1">
                                                        <div className="p-card">
                                                            <div>
                                                                <div
                                                                    className="col-12 flex justify-content-between cur-pointer"
                                                                    onClick={() => {
                                                                        updateSelectedItem(item, first + index);
                                                                    }}
                                                                    style={{
                                                                        flexDirection: "row",
                                                                        background: item.expanded && 'rgba(18, 52, 75, 0.063)',
                                                                        borderBottom:
                                                                            item.expanded === true
                                                                                ? "1px solid gray"
                                                                                : "unset",
                                                                    }}
                                                                >
                                                                    <div
                                                                        className="col-11 flex"
                                                                        style={{ flexDirection: "row" }}
                                                                    >
                                                                        <div className="col-2">
                                                                            <div className="col-12 fw-7 fs-16 text-three-dot">
                                                                                {getCoverageTextById(item, rawsitelist)}
                                                                            </div>
                                                                        </div>
                                                                        <div className="col-8">
                                                                            <div className="col-12 fw-5 fs-16 text-three-dot">
                                                                                {getFormTitle(item)}
                                                                            </div>
                                                                        </div>
                                                                        <div className="col-2 ">
                                                                            {!item.status.includes(0) &&
                                                                                <Tag className={item.status.includes(1) ? "status-tag-red" : item.status.includes(2) ? 'status-tag-orange' : item.status.includes(3) ? 'status-tag-pink' : 'status-tag-blue'}>

                                                                                    {item.status.includes(1) ? 'Resubmission Required' : item.status.includes(2) ? 'Submission Overdue' : item.status.includes(3) ? 'Submission Due' : 'Upcoming'}
                                                                                </Tag>}
                                                                        </div>
                                                                    </div>

                                                                    <div className="col-1 ">
                                                                        {item.expanded === true ? (
                                                                            <i className="material-icons">
                                                                                expand_less
                                                                            </i>
                                                                        ) : (
                                                                            <i className="material-icons">
                                                                                expand_more
                                                                            </i>
                                                                        )}
                                                                    </div>
                                                                </div>
                                                                {item.expanded === true && (
                                                                    <div
                                                                        className="col-12 flex "
                                                                        style={{ flexDirection: "row" }}
                                                                    >
                                                                        <div className="col-3 p-0">
                                                                            <div
                                                                                className="p-card m-1 p-1  flex "
                                                                                style={{
                                                                                    flexDirection: "column",
                                                                                    width: '100%',
                                                                                }}
                                                                            >
                                                                                <div className='flex col-12 p-0 m-1' style={{ flexDirection: 'row', height: item.form_type !== 3 ? 30 : 25, borderBottom: '1px solid gray' }}>

                                                                                    <div className="fw-7 fs-14 flex col-6 p-0 m-1 " >
                                                                                        Datapoint(s)
                                                                                    </div>
                                                                                    <hr
                                                                                        style={{
                                                                                            borderWidth: 2,
                                                                                            margin: 1,
                                                                                        }}
                                                                                    />
                                                                                    <div className="fw-7 fs-14 flex col-6 p-0 m-1 " >
                                                                                        Unit of Measure
                                                                                    </div>
                                                                                </div>
                                                                                {item.dp_array.map((dp, index2) => {
                                                                                    
                                                                                    if (index2 === 0) {
                                                                                        {
                                                                                            return dp.name.map((x, index3) => {
                                                                                                console.log(x)
                                                                                                return (
                                                                                                    <div>
                                                                                                        <div className=" justify-content-center">
                                                                                                            <div className="col-12 flex p-0 m-1" style={{ flexDirection: 'row', height: 30 }}>
                                                                                                                <div
                                                                                                                    className="col-6 fw-4 fs-14 p-0 text-three-dot block m-1 align-items-center cur-pointer"
                                                                                                                    onClick={(e) => { dpnamerefs[index3].current.toggle(e) }}
                                                                                                                >

                                                                                                                    {item.form_type === 2 ? x.label.split('^')[0] : dpNameTemplate(x)}
                                                                                                                </div>
                                                                                                                <hr
                                                                                                                    style={{
                                                                                                                        borderWidth: 1,
                                                                                                                        margin: 1,
                                                                                                                    }}
                                                                                                                />
                                                                                                                <div
                                                                                                                    className="col-6 fw-4 fs-14 p-0 block m-1 text-three-dot align-items-center"

                                                                                                                >

                                                                                                                    {item.form_type === 2 ? x.label.split('^')[1] : getDataPointUnit(x, item.dcfId)}
                                                                                                                </div>

                                                                                                            </div>
                                                                                                            <OverlayPanel key={index} ref={(ref) => { dpnamerefs[index3].current = ref }}>
                                                                                                                {dpNameTemplate(x)}
                                                                                                            </OverlayPanel>


                                                                                                            <hr
                                                                                                                style={{
                                                                                                                    borderWidth: 2,
                                                                                                                    margin: 1,
                                                                                                                }}
                                                                                                            />
                                                                                                        </div>
                                                                                                    </div>
                                                                                                );
                                                                                            });
                                                                                        }
                                                                                    }
                                                                                })}

                                                                                <div style={{ marginTop: 3 }}>
                                                                                    <div className="text-right">
                                                                                        <div className="flex align-items-center fs-16 clr-navy justify-content-end"

                                                                                            style={{ height: 30 }}

                                                                                        >
                                                                                            Current Status
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="text-right">
                                                                                        <div className="flex align-items-center fs-16 clr-navy justify-content-end"

                                                                                            style={{ height: 30 }}

                                                                                        >
                                                                                            Remarks (if any )
                                                                                        </div>
                                                                                    </div>
                                                                                    <div className="text-right">
                                                                                        <div className="flex align-items-center fs-16 clr-navy justify-content-end"

                                                                                            style={{ height: 30 }}

                                                                                        >
                                                                                            Action
                                                                                        </div>
                                                                                    </div>
                                                                                </div>
                                                                            </div>
                                                                        </div>
                                                                        {/* <div
                                                                            className="p-card m-1 p-1  flex "
                                                                            style={{
                                                                                flexDirection: "column",
                                                                                width: 120,
                                                                            }}
                                                                        >
                                                                            <div className="fw-7 fs-16 flex " style={{ height: item.form_type !== 3 ? 30 : 25 }}>
                                                                                Unit(s)
                                                                            </div>
                                                                            {item.dp_array.map((dp, index2) => {
                                                                                
                                                                                if (index2 === 0) {
                                                                                    {
                                                                                        return dp.name.map((x, index3) => {
                                                                                            console.log(x)
                                                                                            return (
                                                                                                <div>
                                                                                                    <div className=" justify-content-center">
                                                                                                        <div
                                                                                                            className="fw-4 fs-14 text-three-dot m-1 align-items-center cur-pointer"
                                                                                                            style={{ height: 30 }}
                                                                                                        >

                                                                                                            {item.form_type === 2 ? x.label.split('^')[1] : ''}
                                                                                                        </div>




                                                                                                        <hr
                                                                                                            style={{
                                                                                                                borderWidth: 2,
                                                                                                                margin: 1,
                                                                                                            }}
                                                                                                        />
                                                                                                    </div>
                                                                                                </div>
                                                                                            );
                                                                                        });
                                                                                    }
                                                                                }
                                                                            })}


                                                                            <div className="text-right">
                                                                                <div className="flex align-items-center fs-16 clr-navy justify-content-end"

                                                                                    style={{ height: 30 }}

                                                                                >

                                                                                </div>
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <div className="flex align-items-center fs-16 clr-navy justify-content-end"

                                                                                    style={{ height: 30 }}

                                                                                >

                                                                                </div>
                                                                            </div>
                                                                            <div className="text-right">
                                                                                <div className="flex align-items-center fs-16 clr-navy justify-content-end"

                                                                                    style={{ height: 30 }}

                                                                                >

                                                                                </div>
                                                                            </div>
                                                                        </div> */}
                                                                        <div className="col-9 p-0">
                                                                            <div
                                                                                style={{
                                                                                    flexDirection: "row",
                                                                                    overflowX: "auto",
                                                                                    display: "-webkit-box", justifyContent: 'space-between'
                                                                                }}
                                                                            >
                                                                                {item.dp_array.map((dp, index2) => {
                                                                                    if (index2 !== 0) {
                                                                                        {
                                                                                            return Object.entries(dp).map((x) => {
                                                                                                
                                                                                                return (
                                                                                                    <div
                                                                                                        className="p-card m-1 p-1  flex"
                                                                                                        style={{
                                                                                                            flexDirection: "column",
                                                                                                            width: 180,
                                                                                                        }}
                                                                                                    >
                                                                                                        <div className="status-tag-gray flex justify-content-center align-items-center p-0 m-1" style={{ height: 30, fontSize: 14, fontWeight: 700 }}>
                                                                                                            {getMonthKey(x[0])}
                                                                                                        </div>
                                                                                                        {x[1].map((z) => {
                                                                                                            console.log(z, item.tier2_id)
                                                                                                            if ((z.form_type === 1 || z.form_type === 2) && (z.dp !== undefined ? z.dp !== null : true)) {


                                                                                                                return (
                                                                                                                    <div>
                                                                                                                        <div className="">
                                                                                                                            <div
                                                                                                                                className="fw-5 fs-14 flex m-1 justify-content-center align-items-center"
                                                                                                                                style={{ height: 30 }}
                                                                                                                            >
                                                                                                                                {z.current}
                                                                                                                                {z.category === 2 && z.diff !== null &&
                                                                                                                                    <>
                                                                                                                                        (
                                                                                                                                        {z.diff ?
                                                                                                                                            <>
                                                                                                                                                <small style={{ color: 'green' }}>{z.percentage}</small>
                                                                                                                                                <i
                                                                                                                                                    className="material-icons"
                                                                                                                                                    style={{
                                                                                                                                                        color: z.current
                                                                                                                                                            ? "green"
                                                                                                                                                            : "white",
                                                                                                                                                        fontSize: 14,
                                                                                                                                                    }}
                                                                                                                                                >
                                                                                                                                                    arrow_downward
                                                                                                                                                </i>
                                                                                                                                            </>
                                                                                                                                            :
                                                                                                                                            <>
                                                                                                                                                <small style={{ color: 'red' }}>{z.percentage}</small>
                                                                                                                                                <i
                                                                                                                                                    className="material-icons"
                                                                                                                                                    style={{
                                                                                                                                                        color: z.current
                                                                                                                                                            ? "red"
                                                                                                                                                            : "white",
                                                                                                                                                        fontSize: 14,
                                                                                                                                                    }}
                                                                                                                                                >
                                                                                                                                                    arrow_upward
                                                                                                                                                </i>
                                                                                                                                            </>
                                                                                                                                        }

                                                                                                                                        )
                                                                                                                                    </>
                                                                                                                                }
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                        <hr
                                                                                                                            style={{
                                                                                                                                borderWidth: 2,
                                                                                                                                margin: 1,
                                                                                                                            }}
                                                                                                                        />
                                                                                                                    </div>
                                                                                                                );
                                                                                                            }
                                                                                                        })}

                                                                                                        {x[1].length !== 0 && x[1][0].id !== undefined
                                                                                                            ? (
                                                                                                                <div style={{ marginTop: 3 }}>
                                                                                                                    <div className="flex justify-content-center" style={{ height: 30 }}>
                                                                                                                        <Tag
                                                                                                                            className={((x[1][0].status === null || x[1][0].status === 0) ? 'status-tag-red'
                                                                                                                                : x[1][0].status === 1 ? 'status-tag-orange'
                                                                                                                                    : (x[1][0].status === 2 || x[1][0].status === 3) ? 'status-tag-blue'
                                                                                                                                        : x[1][0].status === 3 ? "status-tag-green" : "status-tag-green") + ' flex align-items-center'
                                                                                                                            } >

                                                                                                                            {x[1][0].status === null ? 'Pending Submissions' : x[1][0].status === 0 ? 'Drafted' : x[1][0].status === 1 ? 'Requires Resubmissions' : x[1][0].status === 2 ? 'Requires Re-review' : x[1][0].status === 3 ? 'Pending Review' : x[1][0].status === 4 ? 'Pending Approval' : 'Approved'}
                                                                                                                        </Tag>
                                                                                                                    </div>
                                                                                                                    {x[1][0].remarks !== null ?
                                                                                                                        <div className="flex justify-content-center">
                                                                                                                            <div
                                                                                                                                className="cur-pointer text-underline flex align-items-center"
                                                                                                                                style={{ height: 30 }}
                                                                                                                                onClick={() => {
                                                                                                                                    setRemarksData(x[1][0].remarks)
                                                                                                                                    setRemarksDialog(true)
                                                                                                                                }}
                                                                                                                            >
                                                                                                                                View
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                        :
                                                                                                                        <div className="flex justify-content-center">
                                                                                                                            <div
                                                                                                                                style={{ height: 30 }}
                                                                                                                            >

                                                                                                                            </div>
                                                                                                                        </div>

                                                                                                                    }
                                                                                                                    {(x[1][0].status === 0 || x[1][0].status === 1 || x[1][0].status === 2 || x[1][0].status === 3 || x[1][0].status === 4 || x[1][0].status === 5) ?
                                                                                                                        <div className="flex justify-content-center">
                                                                                                                            <div
                                                                                                                                className="cur-pointer text-underline fw-5 fs-16 clr-navy flex align-items-center"
                                                                                                                                style={{ height: 30 }}
                                                                                                                                onClick={() => {

                                                                                                                                    if (x[1][0].id !== undefined) {
                                                                                                                                        window.open(
                                                                                                                                            window.location
                                                                                                                                                .origin +
                                                                                                                                            "/data_input_status/" +
                                                                                                                                            item.dcfId +
                                                                                                                                            "/" +
                                                                                                                                            x[1][0].id
                                                                                                                                        );
                                                                                                                                    }



                                                                                                                                }}
                                                                                                                            >
                                                                                                                                {'View'}
                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                        :

                                                                                                                        <div className="flex justify-content-center">
                                                                                                                            <div

                                                                                                                                style={{ height: 30 }}

                                                                                                                            >

                                                                                                                            </div>
                                                                                                                        </div>
                                                                                                                    }


                                                                                                                </div>
                                                                                                            ) :
                                                                                                            (
                                                                                                                <div style={{ marginTop: 3 }}>
                                                                                                                    <div className="flex justify-content-center">
                                                                                                                        <Tag
                                                                                                                            className="status-tag-red"
                                                                                                                        >

                                                                                                                            {'Pending Submission'}
                                                                                                                        </Tag>
                                                                                                                    </div>
                                                                                                                    <div className="flex justify-content-center">
                                                                                                                        <div

                                                                                                                            style={{ height: 30 }}

                                                                                                                        >

                                                                                                                        </div>
                                                                                                                    </div>

                                                                                                                    <div className="flex justify-content-center">
                                                                                                                        <div className="cur-pointer text-underline fw-5 fs-16 clr-navy flex align-items-center"
                                                                                                                            style={{ height: 30 }}

                                                                                                                        >

                                                                                                                            NA


                                                                                                                        </div>
                                                                                                                    </div>




                                                                                                                </div>
                                                                                                            )


                                                                                                        }
                                                                                                    </div>
                                                                                                );
                                                                                            });
                                                                                        }
                                                                                    }
                                                                                })}
                                                                            </div>
                                                                        </div>
                                                                    </div>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            );
                                        })


                                    }

                                </div >
                            </BlockUI>
                            :
                            <div className="col-12 flex justify-content-center" style={{ height: 'calc(100vh - 300px)' }}>

                                No Data Found
                            </div>
                        }
                        <Paginator first={first} rows={rows} totalRecords={refineddata.length} onPageChange={onPageChange} rowsPerPageOptions={[5, 10, 20]} />
                    </div> :
                        <div>
                            <div className="col-12 flex justify-content-center">
                                <i className="pi pi-spin pi-spinner" style={{ fontSize: 34 }} />
                            </div>
                        </div>
                    }

                </div>
            </div>
            <Dialog visible={remarksdialog} modal
                className="p-fluid"
                onHide={() => {
                    setRemarksDialog(false);
                }} style={{ width: '65%' }} header={'Remarks'} >
                <div>
                    {
                        remarksdata.map((cmnt) => {
                            return (
                                <div className="col-12 grid " style={{ marginBottom: 10, borderBottom: '1px solid gray' }}>
                                    <div className="col-5">
                                        <div>   {cmnt.user_id === login_data.id ? 'You' : getUser(cmnt.user_id)}</div>
                                        <div className="mt-2" > {DateTime.fromISO(cmnt.created_on, { zone: 'utc' }).toLocal().toFormat('dd-LLL-yyyy')} </div>
                                    </div>
                                    <div className="col-5">
                                        {cmnt.remarks}
                                    </div>
                                </div>
                            )
                        })
                    }
                </div>
            </Dialog>
        </div>
    )

}
const comparisonFn = function (prevProps, nextProps) {
    return prevProps.location.pathname === nextProps.location.pathname;
};
export default React.memo(OverallQuantitativeSubmissions, comparisonFn);
