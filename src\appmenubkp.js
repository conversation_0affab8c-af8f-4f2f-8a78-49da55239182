import React, { useEffect, useState } from 'react';
import { Link, useHistory } from 'react-router-dom';
import classNames from 'classnames';
import { useDispatch, useSelector } from 'react-redux';
import { resetLoggedUserDetail } from './RTK/Login/userProfile';
import { motion } from "framer-motion";
import { resetOverallPendingData } from './RTK/Background/pendingData';
import { useLocation } from 'react-router-dom/cjs/react-router-dom';
export const AppTopbar = (props) => {
    const selector = useSelector(state => state.user.userdetail)
    const [rotation, setRotation] = useState(true)
    const history = useHistory()
    const dispatch = useDispatch()
    const login_data = useSelector((state) => state.user.userdetail)
    const location = useLocation()
    const navigate = useHistory()
   
    return (
        <div className="layout-topbar justify-content-between">
            <div className="col-10 flex align-items-center">

                {/* document.querySelector('.sidebar-offcanvas').classList.toggle('active') */}
                {/* {(login_data.information.role === undefined || !login_data.information.role.reporter) && (!sessionStorage.getItem('temp')) && <button type="button" style={{ marginLeft: '-15px' }} className="p-link  layout-menu-button layout-topbar-button" onClick={props.onToggleMenuClick}>

                    <motion.div
                        className="block"
                        onClick={() =>{ setRotation(!rotation);                    document.body.classList.toggle('sidebar-icon-only')   }}
                        animate={{
                            rotate: rotation ? 90 : 0,

                        }}
                    >
                        <i style={{ transform: 'rotate(0deg)' }} className="pi pi-bars" />
                    </motion.div>

                </button>} */}
                <Link to={login_data.role === 'clientadmin' ? "/client/indicators" : '/add_new_client'} style={{ marginLeft: -35,width:300 }} className="justify-content-center layout-topbar-logo">
                    <img id='logo' width={250} height={'auto'} src={props.layoutColorMode === 'light' ? require('../src/assets/images/eisqr_logo_final_v1.png').default : require('../src/assets/images/eisqr_logo.png').default} alt="logo" />
                    {/* <span>Eisqr</span> */}
                </Link>
                {login_data.role === 'clientadmin' &&
                    <div style={{ order: 2 }} className='ml-2 grid'>
                        <div className='m-2'> <label onClick={() => { navigate.push({ pathname: '/client_home' }) }} onMouseLeave={(e) => { e.target.style.background = (location.pathname !== '/client_home' && 'white'); }} onMouseOver={(e) => { e.target.style.background = '#31597510' }} className={(location.pathname === '/client_home' ? 'clr-navy text-bold bg-navy-light ' : 'bg-white ') + 'fs-16  br-5 p-3 cur-pointer '} >Home</label> </div>
                        <div className='m-2'><label onClick={() => { navigate.push({ pathname: '/report_list' }) }} onMouseLeave={(e) => { e.target.style.background = (location.pathname !== '/report_list' && 'white'); }} onMouseOver={(e) => { e.target.style.background = '#31597510' }} className={(location.pathname === '/report_list' ? 'clr-navy text-bold bg-navy-light ' : 'bg-white ') + 'fs-16  br-5 p-3 cur-pointer'}>Reports & Disclosure</label> </div>
                        <div className='m-2'><label onClick={() => { navigate.push({ pathname: '/esg_reports' }) }} onMouseLeave={(e) => { e.target.style.background = (location.pathname !== '/esg_reports' && 'white'); }} onMouseOver={(e) => { e.target.style.background = '#31597510' }} className={(location.pathname === '/esg_reports' ? 'clr-navy text-bold bg-navy-light ' : 'bg-white ') + 'fs-16  br-5 p-3 cur-pointer'}>ESG Reports</label> </div>
                        <div className='m-2'> <label className='fs-16 br-5 text-gray-3 p-3 cur-pointer' >Admin</label> </div>


                    </div>
                }

            </div>
            <div style={{ order: 3 }} className='flex align-items-center '>
                <ul >

                    <li style={{
                        alignItems: 'center',
                        display: 'flex'
                    }}>
                        <button className="p-link layout-topbar-button" onClick={props.onMobileSubTopbarMenuClick}>
                            {Object.keys(selector).length !== 0 && (selector.role === 'clientadmin' || selector.role === 'eisqradmin') ? <img id='clientlogo' src={selector.information.companylogo} style={{
                                width: '100%', maxWidth: 50,
                                borderRadius: '50%'
                            }} /> : <i className="pi pi-user" />}
                            {/* <span>{Object.keys(selector).length !== 0 && (selector.role === 'clientadmin' ? ` (Super Admin)` : selector.role === 'eisqradmin' ? 'Eisqr (SuperAdmin)' : selector.information.empname + `(${selector.information.role.reporter ? 'Reporter' : 'Approver'})`)}</span> */}

                        </button>
                        {/* <span style={{ marginLeft: 10 }}>{Object.keys(selector).length !== 0 && (selector.role === 'clientadmin' ? ` (Super Admin)` : selector.role === 'eisqradmin' ? 'Eisqr (SuperAdmin)' : selector.information.empname + `(${selector.information.role.reporter ? 'Reporter' : 'Approver'})`)}</span> */}

                        {/* <button style={{ marginLeft: 10 }} onClick={() => { dispatch(resetLoggedUserDetail()); dispatch(resetOverallPendingData([])); localStorage.clear();   sessionStorage.clear() ; history.push('/') }} className='p-link'><i className='pi pi-power-off clr-delete' style={{fontSize:20}} ></i> </button> */}
                    </li>
                </ul>
                {/* <button type="button" className="p-link layout-topbar-button ml-2" onClick={props.onMobileTopbarMenuClick}>
                    <i className="pi pi-cog" />
                </button> */}
            </div>

            {/* layout-topbar-menu lg:flex origin-top */}

            {/* layout-topbar-menu lg:flex origin-top layout-topbar-menu-mobile-active */}
            <ul style={{ listStyle: 'none' }} className={props.mobileTopbarMenuActive ? 'custom-layout-topbar-menu lg:flex origin-top layout-topbar-menu-mobile-active' : 'layout-topbar-menu lg:flex origin-top d-none'}>
                <li >
                    <button style={{ width: '100%' }} className="p-link layout-topbar-button justify-content-start " >
                        <i className="pi pi-key " style={{fontSize:14}}  />
                        <span className='ml-3'>Change Password</span>
                    </button>
                </li>
                <li >

                    <button style={{ width: '100%' }} onClick={() => { dispatch(resetLoggedUserDetail()); dispatch(resetOverallPendingData([])); localStorage.clear(); sessionStorage.clear(); history.push('/') }} className="p-link layout-topbar-button justify-content-start">
                        <i className="pi pi-sign-out clr-delete" style={{fontSize:14}} />
                        <span className='ml-3'>Logout</span>
                    </button>
                </li>

            </ul>
        </div>
    );
}
