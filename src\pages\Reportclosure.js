import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import APIServices from "../../service/APIService";
import { API } from "../../components/constants/api_url";
import { useSelector } from "react-redux";

const baseReports = [
  { type: "Internal Framework", name: "ESG" },
  { type: "Internal Framework", name: "Customer" },
  { type: "External Framework", name: "Audit" },
];

function Chevron({ expanded, direction = "right" }) {
  return (
    <svg
      width="27"
      height="27"
      viewBox="0 0 24 24"
      style={{
        transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
        transition: "transform 0.2s ease",
        marginLeft: direction === "right" ? "8px" : "0",
        marginRight: direction === "left" ? "8px" : "0",
        flexShrink: 0,
      }}
    >
      <path
        d="M8 10l4 4 4-4"
        fill="none"
        stroke="#444"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function Reportclosure() {
  const [expandedCountry, setExpandedCountry] = useState({});
  const [expandedType, setExpandedType] = useState({});
  const [selectedReport, setSelectedReport] = useState({ country: "", name: "" });
  const [userRole, setUserRole] = useState(null);
  const [userCountries, setUserCountries] = useState([]);
  const [dynamicReportData, setDynamicReportData] = useState([]);

  const login_data = useSelector((state) => state.user.userdetail);

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const fetchUserInfo = async () => {
    try {
      const roleRes = await APIServices.get(API.GetRoleByUserId(94, 112));
      const role = roleRes.data;

      const isCountryAdmin = role.some((item) => item.roles.includes(6) && item.tier2_id === 0);
      const isCorporateAdmin = role.some((item) => item.roles.includes(24) && item.tier1_id === 0);
      const filterCountry = role.filter((item) => item.roles.includes(6) && item.tier2_id === 0);

      if (isCorporateAdmin) {
        setUserRole("corporate");
      } else if (isCountryAdmin) {
        setUserRole("country");
      } else {
        setUserRole("none");
      }

      const uriString = {
        include: [
          {
            relation: "locationTwos",
            scope: { include: [{ relation: "locationThrees" }] },
          },
        ],
      };

      const promise2 = await APIServices.get(
        API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
      );

      const shapedSite = promise2.data
        .map((item) => {
          if (item.locationTwos) {
            item.locationTwos = item.locationTwos.filter(
              (locationTwo) =>
                locationTwo.locationThrees && locationTwo.locationThrees.length > 0
            );
          }
          return item;
        })
        .filter((item) => item.locationTwos && item.locationTwos.length > 0);

      const allowedCountries = isCorporateAdmin
        ? shapedSite
        : shapedSite.filter((site) => filterCountry.map((item) => item.tier1_id).includes(site.id));
            setUserCountries(allowedCountries);
// Dynamically generate reports for each allowed country
      const dynamicReports = [];

      allowedCountries.forEach((country) => {
        baseReports.forEach((report) => {
          dynamicReports.push({
            Country: country.name,
            "Type of Report": report.type,
            "Report Name": report.name,
          });
        });
      });

      setDynamicReportData(dynamicReports);
    } catch (err) {
      console.error("Error fetching user info:", err);
      setUserRole("none");
    }
  };

  const filteredReportData = dynamicReportData.filter((report) => {
    const countryName = report.Country.trim().toLowerCase();
    if (userRole === "corporate") return true;
    if (userRole === "country") {
      return userCountries
        .map((c) => c.name.trim().toLowerCase())
        .includes(countryName);
    }
    return false;
  });

  const groupedData = filteredReportData.reduce((acc, item) => {
    const { Country, "Type of Report": type, "Report Name": name } = item;
    if (!acc[Country]) acc[Country] = {};
    if (!acc[Country][type]) acc[Country][type] = [];
    acc[Country][type].push(name);
    return acc;
  }, {});

  const toggleCountry = (country) =>
    setExpandedCountry((prev) => ({ ...prev, [country]: !prev[country] }));

  const toggleType = (country, type) =>
    setExpandedType((prev) => ({
      ...prev,
      [`${country}-${type}`]: !prev[`${country}-${type}`],
    }));

  const countryList = Object.entries(groupedData).filter(
    ([, types]) => Object.keys(types).length > 0
  );

  if (userRole === "none") {
    return (
      <div style={{ padding: "24px", fontFamily: "Arial, sans-serif" }}>
        <h3>You are not configured as an Admin.</h3>
        <p>Please contact your Corporate Admin to get access.</p>
      </div>
    );
  }

  return (
    <div style={{ fontFamily: "Arial, sans-serif", fontSize: "14px" }}>
      <div
        style={{
          backgroundColor: "#f9f9f9",
          padding: "16px 24px",
          display: "flex",
          justifyContent: "space-between",
        }}
      >
        <div>
          <div style={{ fontSize: "16px", fontWeight: "bold" }}>Reports & Disclosure</div>
          <div style={{ fontSize: "13px", color: "#555" }}>
            {userRole === "corporate"
              ? "Corporate Admin: Viewing all countries"
              : `Viewing reports for: ${userCountries.map((c) => c.name).join(", ")}`}
          </div>
        </div>
      </div>

      <div style={{ display: "flex", height: "calc(100vh - 70px)" }}>
        {/* Left Pane */}
        <div
          style={{
            width: "300px",
            background: "#f9f9f9",
            borderRight: "1px solid #ddd",
            padding: "8px",
            overflowY: "auto",
          }}
        >
          {countryList.map(([country, types]) => (
            <div key={country} style={{ border: "1px solid #ccc", marginBottom: "0px" }}>
              <div
                onClick={() => toggleCountry(country)}
                style={{
                  padding: "10px 12px",
                  backgroundColor: "#e6e6e6",
                  fontWeight: "bold",
                  color: "#333",
                  display: "flex",
                  justifyContent: "space-between",
                  cursor: "pointer",
                }}
              >
                <span>{country.toUpperCase()}</span>
                <Chevron expanded={expandedCountry[country]} direction="right" />
              </div>

              {expandedCountry[country] &&
                Object.entries(types).map(([type, reports]) => {
                  const typeKey = `${country}-${type}`;
                  return (
                    <div key={type}>
                      <div
                        onClick={() => toggleType(country, type)}
                        style={{
                          padding: "8px 16px",
                          backgroundColor: "#f5f5f5",
                          fontWeight: "600",
                          display: "flex",
                          alignItems: "center",
                          cursor: "pointer",
                        }}
                      >
                        <Chevron expanded={expandedType[typeKey]} direction="left" />
                        {type}
                      </div>
                      {expandedType[typeKey] && (
                        <ul style={{ listStyle: "none", paddingLeft: "64px", margin: "4px 0" }}>
                          {reports.map((report, i) => {
                            const isSelected =
                              selectedReport.country === country &&
                              selectedReport.name === report;
                            return (
                              <li
                                key={i}
                                onClick={() => setSelectedReport({ country, name: report })}
                                style={{
                                  padding: "6px 8px",
                                  cursor: "pointer",
                                  backgroundColor: isSelected ? "#e0ecff" : "transparent",
                                  color: isSelected ? "#0d47a1" : "#333",
                                  borderRight: isSelected ? "3px solid #0d47a1" : "none",
                                }}
                              >
                                {report}
                              </li>
                            );
                          })}
                        </ul>
                      )}
                    </div>
                  );
                })}
            </div>
          ))}
        </div>

        {/* Right Pane */}
        <div style={{ flex: 1, padding: "24px", overflowY: "auto" }}>
          <h2 style={{ fontSize: "20px", fontWeight: "bold", marginBottom: "16px" }}>
            {selectedReport.name ? `${selectedReport.name} Report` : "Select a Report"}
          </h2>
          <p style={{ color: "#666" }}>
            {selectedReport.name
              ? `Report for ${selectedReport.country} - ${selectedReport.name}`
              : "Choose a report from the left panel."}
          </p>
        </div>
      </div>
    </div>
  );
}

export default Reportclosure;
