import React, { useState, useEffect } from "react";
import PropTypes from "prop-types";
import APIServices from "../../service/APIService";
import { API } from "../../components/constants/api_url";
import { useSelector } from "react-redux";

const baseReports = [
  { type: "Internal Framework", name: "ESG" },
  { type: "Internal Framework", name: "Customer" },
  { type: "External Framework", name: "Audit" },
];

function Chevron({ expanded, direction = "right" }) {
  return (
    <svg
      width="27"
      height="27"
      viewBox="0 0 24 24"
      style={{
        transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
        transition: "transform 0.2s ease",
        marginLeft: direction === "right" ? "8px" : "0",
        marginRight: direction === "left" ? "8px" : "0",
        flexShrink: 0,
      }}
    >
      <path
        d="M8 10l4 4 4-4"
        fill="none"
        stroke="#444"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

function Reportclosure() {
  const [expandedType, setExpandedType] = useState({});
  const [selectedReport, setSelectedReport] = useState({ country: "", name: "" });
  const [userRole, setUserRole] = useState(null);
  const [userCountries, setUserCountries] = useState([]);
  const [dynamicReportData, setDynamicReportData] = useState([]);

  const login_data = useSelector((state) => state.user.userdetail);

  useEffect(() => {
    fetchUserInfo();
  }, []);

  const fetchUserInfo = async () => {
    try {
      const roleRes = await APIServices.get(API.GetRoleByUserId(94, 112));
      const role = roleRes.data;

      const isCountryAdmin = role.some((item) => item.roles.includes(6) && item.tier2_id === 0);
      const isCorporateAdmin = role.some((item) => item.roles.includes(24) && item.tier1_id === 0);
      const filterCountry = role.filter((item) => item.roles.includes(6) && item.tier2_id === 0);

      if (isCorporateAdmin) {
        setUserRole("corporate");
      } else if (isCountryAdmin) {
        setUserRole("country");
      } else {
        setUserRole("none");
      }

      const uriString = {
        include: [
          {
            relation: "locationTwos",
            scope: { include: [{ relation: "locationThrees" }] },
          },
        ],
      };

      const promise2 = await APIServices.get(
        API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
      );

      const shapedSite = promise2.data
        .map((item) => {
          if (item.locationTwos) {
            item.locationTwos = item.locationTwos.filter(
              (locationTwo) =>
                locationTwo.locationThrees && locationTwo.locationThrees.length > 0
            );
          }
          return item;
        })
        .filter((item) => item.locationTwos && item.locationTwos.length > 0);

      const allowedCountries = isCorporateAdmin
        ? shapedSite
        : shapedSite.filter((site) => filterCountry.map((item) => item.tier1_id).includes(site.id));
            setUserCountries(allowedCountries);

      const dynamicReports = [];

      allowedCountries.forEach((country) => {
        baseReports.forEach((report) => {
          dynamicReports.push({
            Country: country.name,
            "Type of Report": report.type,
            "Report Name": report.name,
          });
        });
      });

      setDynamicReportData(dynamicReports);
    } catch (err) {
      console.error("Error fetching user info:", err);
      setUserRole("none");
    }
  };

  const filteredReportData = dynamicReportData.filter((report) => {
    const countryName = report.Country.trim().toLowerCase();
    if (userRole === "corporate") return true;
    if (userRole === "country") {
      return userCountries
        .map((c) => c.name.trim().toLowerCase())
        .includes(countryName);
    }
    return false;
  });

  const groupedData = filteredReportData.reduce((acc, item) => {
    const { Country, "Type of Report": type, "Report Name": name } = item;
    if (!acc[Country]) acc[Country] = {};
    if (!acc[Country][type]) acc[Country][type] = [];
    acc[Country][type].push(name);
    return acc;
  }, {});

  const toggleType = (country, type) =>
    setExpandedType((prev) => ({
      ...prev,
      [`${country}-${type}`]: !prev[`${country}-${type}`],
    }));

  const countryList = Object.entries(groupedData).filter(
    ([, types]) => Object.keys(types).length > 0
  );

  if (userRole === "none") {
    return (
      <div className="grid bg-smoke" style={{ minHeight: '100vh', padding: '20px' }}>
        <div className="col-12">
          <div className="card p-4 text-center">
            <h3 className="text-bold fs-18 mb-3">Access Denied</h3>
            <p className="fs-14 clr-gray-3">You are not configured as an Admin.</p>
            <p className="fs-14 clr-gray-3">Please contact your Corporate Admin to get access.</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid bg-smoke" style={{ minHeight: '100vh', padding: '20px' }}>
      {/* Left Sidebar Navigator */}
      <div className="col-3">
        <div className="card p-3" style={{ height: 'fit-content' }}>
          <h3 className="text-bold fs-18 mb-3">Report Navigator</h3>
          
          {countryList.map(([country, types]) => (
            <div key={country} className="mb-3">
              <label className="text-bold fs-14 mb-2 block">{country.toUpperCase()}</label>
              <div className="ml-3">
                {Object.entries(types).map(([type, reports]) => {
                  const typeKey = `${country}-${type}`;
                  return (
                    <div key={type} className="mb-2">
                      <div 
                        className="flex align-items-center cur-pointer mb-1"
                        onClick={() => toggleType(country, type)}
                      >
                        <Chevron expanded={expandedType[typeKey]} direction="left" />
                        <label className="text-bold fs-12 mb-1 block ml-1">{type}</label>
                      </div>
                      {expandedType[typeKey] && (
                        <div className="ml-3">
                          {reports.map((report, i) => {
                            const isSelected =
                              selectedReport.country === country &&
                              selectedReport.name === report;
                            return (
                              <div
                                key={i}
                                className={`p-2 br-5 mb-1 cur-pointer ${
                                  isSelected ? 'bg-navy-light' : 'hover-blue'
                                }`}
                                onClick={() => setSelectedReport({ country, name: report })}
                              >
                                <span className={`fs-12 ${isSelected ? 'clr-navy text-bold' : ''}`}>
                                  {report}
                                </span>
                              </div>
                            );
                          })}
                        </div>
                      )}
                    </div>
                  );
                })}
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Main Content Area */}
      <div className="col-9">
        <div className="card p-4">
          {/* Header */}
          <div className="flex justify-content-between align-items-center mb-4">
            <div>
              <h2 className="text-bold fs-24 mb-1">Reports & Disclosure</h2>
              <span className="clr-gray-3 fs-14">
                {userRole === "corporate"
                  ? "Corporate Admin • Viewing all countries"
                  : `Country Admin • Viewing reports for: ${userCountries.map((c) => c.name).join(", ")}`}
              </span>
            </div>
            <div className="text-right">
              <div className="text-bold fs-18">
                {selectedReport.name ? `${selectedReport.name} Report` : "Select a Report"}
              </div>
              <div className="clr-gray-3 fs-12">
                {selectedReport.country ? `${selectedReport.country}` : "Choose from left panel"}
              </div>
            </div>
          </div>

          {/* Report Content */}
          <div className="mb-4">
            <h3 className="text-bold fs-18 mb-3">Report Details</h3>
            <p className="clr-gray-3 fs-14 mb-3">
              {selectedReport.name
                ? `Viewing ${selectedReport.name} report for ${selectedReport.country}`
                : "Select a report from the navigator to view details and manage report closure."}
            </p>

            {selectedReport.name && (
              <div className="card bg-white p-4">
                <h4 className="text-bold fs-16 mb-3">
                  {selectedReport.name} Report - {selectedReport.country}
                </h4>
                <div className="grid">
                  <div className="col-6">
                    <div className="mb-3">
                      <label className="text-bold fs-12 mb-2 block">Report Status</label>
                      <div className="p-2 bg-yellow-50 br-5">
                        <span className="fs-12 text-yellow-600">Pending Closure</span>
                      </div>
                    </div>
                  </div>
                  <div className="col-6">
                    <div className="mb-3">
                      <label className="text-bold fs-12 mb-2 block">Last Updated</label>
                      <div className="p-2 bg-gray-50 br-5">
                        <span className="fs-12">Jun 11, 2025</span>
                      </div>
                    </div>
                  </div>
                </div>
                <div className="mt-3">
                  <p className="fs-14 clr-gray-3">
                    Report closure functionality will be implemented here. This will include 
                    options to review, approve, and close the selected report.
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}

export default Reportclosure;
