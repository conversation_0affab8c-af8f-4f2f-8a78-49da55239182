import { Dropdown } from "primereact/dropdown";
import React, { useState } from "react";

const CustomDropdownEditor = (props) => {
  const [selectedValue, setSelectedValue] = useState(props.value);

  const handleDropdownChange = (e) => {
    const newValue = e.value;
    setSelectedValue(newValue);
    props.editorCallback(newValue);
  };

  return (
    <Dropdown
      value={selectedValue}
      options={props.options}
      optionLabel={props.optionLabel}
      optionValue={props.optionValue}
      onChange={handleDropdownChange}
      placeholder="Select"
    />
  );
};

export default CustomDropdownEditor;
