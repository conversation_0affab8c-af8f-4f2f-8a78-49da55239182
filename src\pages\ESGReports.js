import React, { useState, useEffect } from 'react';
import PropTypes from "prop-types";
import APIServices from "../../service/APIService";
import { API } from "../../components/constants/api_url";
import { Dropdown } from 'primereact/dropdown';
import { Button } from 'primereact/button';
import { Calendar } from 'primereact/calendar';
import { useSelector } from 'react-redux';
import { useHistory } from 'react-router-dom';
import Highcharts from 'highcharts';
import HighchartsReact from 'highcharts-react-official';
import { MultiSelect } from 'primereact/multiselect';

// Starting year for dynamic year generation
const STARTING_YEAR = 2020;

// Base reports configuration
const baseReports = [
  { type: "Internal Framework", name: "ESG" },
  { type: "Internal Framework", name: "Customer" },
  { type: "External Framework", name: "Audit" },
];

// Chevron component for expandable sections
function Chevron({ expanded, direction = "right" }) {
  return (
    <svg
      width="27"
      height="27"
      viewBox="0 0 24 24"
      style={{
        transform: expanded ? "rotate(180deg)" : "rotate(0deg)",
        transition: "transform 0.2s ease",
        marginLeft: direction === "right" ? "8px" : "0",
        marginRight: direction === "left" ? "8px" : "0",
        flexShrink: 0,
      }}
    >
      <path
        d="M8 10l4 4 4-4"
        fill="none"
        stroke="#444"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
}

const ESGReports = () => {
    // Reportclosure states
    const [expandedCountry, setExpandedCountry] = useState({});
    const [expandedType, setExpandedType] = useState({});
    const [selectedReport, setSelectedReport] = useState({ country: "", name: "" });
    const [userRole, setUserRole] = useState(null);
    const [userCountries, setUserCountries] = useState([]);
    const [dynamicReportData, setDynamicReportData] = useState([]);

    // ESG Reports filter states
    const [selectedYear, setSelectedYear] = useState('2024');
    const [selectedCountry, setSelectedCountry] = useState('Global');
    const [selectedPeriod, setSelectedPeriod] = useState('Monthly');
    const [selectedFramework, setSelectedFramework] = useState('Global');
    const [selectedCategory, setSelectedCategory] = useState('ESG');

    // Custom date range for reporting period
    const [startDate, setStartDate] = useState(null);
    const [endDate, setEndDate] = useState(null);

    // Individual filters for specific sections
    const [energyWithdrawalYear, setEnergyWithdrawalYear] = useState('2024');
    const [waterWithdrawalCountry, setWaterWithdrawalCountry] = useState('Global');

    const selector = useSelector(state => state.user.userdetail);
    const login_data = useSelector((state) => state.user.userdetail);
    const navigate = useHistory();

    const [selectedEnergyYears, setSelectedEnergyYears] = useState([]);
    const [selectedWaterEntities, setSelectedWaterEntities] = useState(['Global']);

    // Reportclosure API integration
    useEffect(() => {
        fetchUserInfo();
    }, []);

    const fetchUserInfo = async () => {
        try {
            const roleRes = await APIServices.get(API.GetRoleByUserId(94, 112));
            const role = roleRes.data;

            const isCountryAdmin = role.some((item) => item.roles.includes(6) && item.tier2_id === 0);
            const isCorporateAdmin = role.some((item) => item.roles.includes(24) && item.tier1_id === 0);
            const filterCountry = role.filter((item) => item.roles.includes(6) && item.tier2_id === 0);

            if (isCorporateAdmin) {
                setUserRole("corporate");
            } else if (isCountryAdmin) {
                setUserRole("country");
            } else {
                setUserRole("none");
            }

            const uriString = {
                include: [
                    {
                        relation: "locationTwos",
                        scope: { include: [{ relation: "locationThrees" }] },
                    },
                ],
            };

            const promise2 = await APIServices.get(
                API.LocationOne_UP(94) + `?filter=${encodeURIComponent(JSON.stringify(uriString))}`
            );

            const shapedSite = promise2.data
                .map((item) => {
                    if (item.locationTwos) {
                        item.locationTwos = item.locationTwos.filter(
                            (locationTwo) =>
                                locationTwo.locationThrees && locationTwo.locationThrees.length > 0
                        );
                    }
                    return item;
                })
                .filter((item) => item.locationTwos && item.locationTwos.length > 0);

            const allowedCountries = isCorporateAdmin
                ? shapedSite
                : shapedSite.filter((site) => filterCountry.map((item) => item.tier1_id).includes(site.id));

            setUserCountries(allowedCountries);

            // Dynamically generate reports for each allowed country
            const dynamicReports = [];

            allowedCountries.forEach((country) => {
                baseReports.forEach((report) => {
                    dynamicReports.push({
                        Country: country.name,
                        "Type of Report": report.type,
                        "Report Name": report.name,
                    });
                });
            });

            setDynamicReportData(dynamicReports);
        } catch (err) {
            console.error("Error fetching user info:", err);
            setUserRole("none");
        }
    };

    // Reportclosure helper functions
    const filteredReportData = dynamicReportData.filter((report) => {
        const countryName = report.Country.trim().toLowerCase();
        if (userRole === "corporate") return true;
        if (userRole === "country") {
            return userCountries
                .map((c) => c.name.trim().toLowerCase())
                .includes(countryName);
        }
        return false;
    });

    const groupedData = filteredReportData.reduce((acc, item) => {
        const { Country, "Type of Report": type, "Report Name": name } = item;
        if (!acc[Country]) acc[Country] = {};
        if (!acc[Country][type]) acc[Country][type] = [];
        acc[Country][type].push(name);
        return acc;
    }, {});

    const toggleCountry = (country) =>
        setExpandedCountry((prev) => ({ ...prev, [country]: !prev[country] }));

    const toggleType = (country, type) =>
        setExpandedType((prev) => ({
            ...prev,
            [`${country}-${type}`]: !prev[`${country}-${type}`],
        }));

    const countryList = Object.entries(groupedData).filter(
        ([, types]) => Object.keys(types).length > 0
    );

    // Generate year options dynamically from starting year to current year
    const generateYearOptions = () => {
        const currentYear = new Date().getFullYear();
        const years = [];
        for (let year = currentYear; year >= STARTING_YEAR; year--) {
            years.push({ label: year.toString(), value: year.toString() });
        }
        return years;
    };

    const yearOptions = generateYearOptions();

    // STT configured countries
    const countryOptions = [
        { label: 'Global', value: 'Global' },
        { label: 'India', value: 'India' },
        { label: 'Singapore', value: 'Singapore' },
        { label: 'UK', value: 'UK' }
    ];

    const periodOptions = [
        { label: 'Monthly', value: 'Monthly' },
        { label: 'Quarterly', value: 'Quarterly' },
        { label: 'Half yearly', value: 'Half yearly' },
        { label: 'Yearly', value: 'Yearly' },
        { label: 'Custom', value: 'Custom' }
    ];

    const frameworkOptions = [
        { label: 'Global', value: 'Global' },
        { label: 'Internal Framework', value: 'Internal Framework' },
        { label: 'External Framework', value: 'External Framework' }
    ];

    const categoryOptions = [
        { label: 'ESG', value: 'ESG' },
        { label: 'Customer', value: 'Customer' },
        { label: 'External Framework', value: 'External Framework' }
    ];

    // Highcharts configurations
    const [pieChartOptions, setPieChartOptions] = useState({});
    const [barChartOptions, setBarChartOptions] = useState({});
    const [energyBarChartOptions, setEnergyBarChartOptions] = useState({});
    const [waterBarChartOptions, setWaterBarChartOptions] = useState({});

    // Monthly breakdown data
    const monthlyData = [
        { month: 'Jan', value: 1141 },
        { month: 'Feb', value: 1488 },
        { month: 'Mar', value: 1349 }
    ];

    // Energy spend breakdown data
    const energySpendData = [
        { category: 'Electricity', percentage: 45, color: '#FF6B35' },
        { category: 'Natural Gas', percentage: 25, color: '#F7931E' },
        { category: 'Diesel', percentage: 15, color: '#FFD23F' },
        { category: 'Petrol', percentage: 10, color: '#06D6A0' },
        { category: 'Coal', percentage: 5, color: '#118AB2' }
    ];

    // Carbon breakdown data
    const carbonBreakdownData = [
        { source: 'Electricity', percentage: 65.89, color: '#FF6B35' },
        { source: 'Natural Gas', percentage: 21.43, color: '#F7931E' },
        { source: 'Diesel', percentage: 8.76, color: '#FFD23F' },
        { source: 'Petrol', percentage: 2.87, color: '#06D6A0' },
        { source: 'Coal', percentage: 1.05, color: '#118AB2' }
    ];

    // Energy spend breakdown data
    const energySpendData = [
        { category: 'Electricity', percentage: 45, color: '#FF6B35' },
        { category: 'Natural Gas', percentage: 25, color: '#F7931E' },
        { category: 'Diesel', percentage: 15, color: '#FFD23F' },
        { category: 'Petrol', percentage: 10, color: '#06D6A0' },
        { category: 'Coal', percentage: 5, color: '#118AB2' }
    ];

    // Carbon breakdown data
    const carbonBreakdownData = [
        { source: 'Electricity', percentage: 65.89, color: '#FF6B35' },
        { source: 'Natural Gas', percentage: 21.43, color: '#F7931E' },
        { source: 'Diesel', percentage: 8.76, color: '#FFD23F' },
        { source: 'Petrol', percentage: 2.87, color: '#06D6A0' },
        { source: 'Coal', percentage: 1.05, color: '#118AB2' }
    ];

    // New multi-select filters
    const [selectedEnergyFilters, setSelectedEnergyFilters] = useState([]);
    const [selectedWaterFilters, setSelectedWaterFilters] = useState([]);

    const energyFilterOptions = [
        { label: 'Electricity', value: 'electricity' },
        { label: 'Natural Gas', value: 'natural_gas' },
        { label: 'Diesel', value: 'diesel' },
        { label: 'Petrol', value: 'petrol' },
        { label: 'Coal', value: 'coal' }
    ];

    const waterFilterOptions = [
        { label: 'Ground Water', value: 'ground_water' },
        { label: 'Surface Water', value: 'surface_water' },
        { label: 'Municipal Water', value: 'municipal_water' },
        { label: 'Recycled Water', value: 'recycled_water' }
    ];

    const energyFilterOptions = [
        { label: 'Electricity', value: 'electricity' },
        { label: 'Natural Gas', value: 'natural_gas' },
        { label: 'Diesel', value: 'diesel' },
        { label: 'Petrol', value: 'petrol' },
        { label: 'Coal', value: 'coal' }
    ];

    const waterFilterOptions = [
        { label: 'Ground Water', value: 'ground_water' },
        { label: 'Surface Water', value: 'surface_water' },
        { label: 'Municipal Water', value: 'municipal_water' },
        { label: 'Recycled Water', value: 'recycled_water' }
    ];

    // Helper function to get breakdown type based on selected period
    const getBreakdownType = () => {
        switch (selectedPeriod) {
            case 'Monthly':
                return 'Monthly Breakdown (All 12 Months)';
            case 'Quarterly':
                return 'Quarterly Breakdown (Jan-Mar)';
            case 'Half yearly':
                return 'Half Yearly Breakdown (First 6 Months)';
            case 'Yearly':
                return 'Yearly Summary';
            case 'Custom':
                return 'Custom Period Breakdown';
            default:
                return 'Monthly Breakdown (All 12 Months)';
        }
    };

    // Generate monthly breakdown based on selected period
    const generateMonthlyBreakdown = () => {
        const baseValue = 1200;
        const baseMultiplier = selectedYear === '2024' ? 1.1 : selectedYear === '2023' ? 1.0 : 0.9;
        const countryMultiplier = selectedCountry === 'Global' ? 1.0 : selectedCountry === 'India' ? 1.2 : selectedCountry === 'Singapore' ? 0.9 : 0.8;

        let months = [];

        if (selectedPeriod === 'Monthly') {
            // Show all 12 months
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        } else if (selectedPeriod === 'Quarterly') {
            // Show first 3 months (January, February, March)
            months = ['Jan', 'Feb', 'Mar'];
        } else if (selectedPeriod === 'Half yearly') {
            // Show first 6 months
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
        } else if (selectedPeriod === 'Yearly') {
            // Show single column representing full year
            months = ['Year'];
        } else if (selectedPeriod === 'Custom' && startDate && endDate) {
            // Generate months between start and end date
            const start = new Date(startDate);
            const end = new Date(endDate);
            const monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];

            months = [];
            const current = new Date(start.getFullYear(), start.getMonth(), 1);
            const endMonth = new Date(end.getFullYear(), end.getMonth(), 1);

            while (current <= endMonth) {
                months.push(monthNames[current.getMonth()]);
                current.setMonth(current.getMonth() + 1);
            }
        } else {
            // Default to all 12 months if no valid period selected
            months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        }

        return months.map(month => ({
            month,
            value: Math.round(baseValue * baseMultiplier * countryMultiplier * (0.8 + Math.random() * 0.4))
        }));
    };

    // Dynamic data calculation based on filters
    const calculateDynamicData = () => {
        const baseMultiplier = selectedYear === '2024' ? 1.1 : selectedYear === '2023' ? 1.0 : 0.9;
        const countryMultiplier = selectedCountry === 'Global' ? 1.0 : selectedCountry === 'India' ? 1.2 : selectedCountry === 'Singapore' ? 0.9 : 0.8;

        // Calculate period multiplier based on new period options
        let periodMultiplier = 1.0;
        if (selectedPeriod === 'Monthly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Quarterly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Half yearly') periodMultiplier = 0.5;
        else if (selectedPeriod === 'Yearly') periodMultiplier = 1.0;
        else if (selectedPeriod === 'Custom') periodMultiplier = 0.5;

        // Calculate renewable energy percentage based on country and year
        const renewableBase = selectedCountry === 'Global' ? 50 : selectedCountry === 'India' ? 45 : selectedCountry === 'Singapore' ? 65 : 55;
        const renewableYearAdjustment = selectedYear === '2024' ? 5 : selectedYear === '2023' ? 0 : -5;
        const renewablePercentage = Math.min(100, Math.max(0, renewableBase + renewableYearAdjustment));

        const dynamicMonthlyData = generateMonthlyBreakdown();

        return {
            itLoad: Math.round(7378 * baseMultiplier * countryMultiplier),
            fuelConsumption: Math.round(5567 * baseMultiplier * countryMultiplier),
            waterConsumption: Math.round(7647 * baseMultiplier * countryMultiplier * periodMultiplier),
            renewablePercentage: renewablePercentage,
            monthlyData: dynamicMonthlyData
        };
    };



    useEffect(() => {
        const dynamicData = calculateDynamicData();

        // Pie chart configuration for Energy Spend Breakdown
        const pieOptions = {
            chart: {
                type: 'pie',
                height: 300
            },
            title: {
                text: null
            },
            plotOptions: {
                pie: {
                    allowPointSelect: true,
                    cursor: 'pointer',
                    dataLabels: {
                        enabled: true,
                        format: '<b>{point.name}</b>: {point.percentage:.1f} %'
                    },
                    showInLegend: true
                }
            },
            legend: {
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal'
            },
            series: [{
                name: 'Energy Spend',
                colorByPoint: true,
                data: energySpendData.map(item => ({
                    name: item.category,
                    y: item.percentage,
                    color: item.color
                }))
            }]
        };

        // Bar chart configuration for Energy Withdrawal
        const energyBarOptions = {
            chart: {
                type: 'column',
                height: 300
            },
            title: {
                text: null
            },
            xAxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                crosshair: true
            },
            yAxis: {
                min: 0,
                title: {
                    text: 'Energy (MWh)'
                }
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0
                }
            },
            series: [{
                name: 'Energy Withdrawal',
                data: [
                    dynamicData.monthlyData[0]?.value || 0,
                    dynamicData.monthlyData[1]?.value || 0,
                    dynamicData.monthlyData[2]?.value || 0,
                    0, 0, 0
                ],
                color: '#3B82F6'
            }]
        };

        // Bar chart configuration for Water Withdrawal
        const generateWaterWithdrawalData = () => {
            const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            const baseValue = 11090;
            const colors = ['#06D6A0', '#118AB2', '#FFD23F', '#F7931E', '#FF6B35'];

            return selectedWaterEntities.map((entity, index) => {
                const entityMultiplier = entity === 'Global' ? 1.0 :
                                       entity === 'India' ? 1.2 :
                                       entity === 'Singapore' ? 0.8 :
                                       entity === 'UK' ? 0.9 : 1.0;

                const data = months.map(() =>
                    Math.round(baseValue * entityMultiplier * (0.8 + Math.random() * 0.4))
                );

                return {
                    name: entity,
                    data: data,
                    color: colors[index % colors.length]
                };
            });
        };

        const waterBarOptions = {
            chart: {
                type: 'column',
                height: 380,
                marginBottom: 60,
                marginTop: 20
            },
            title: {
                text: null
            },
            xAxis: {
                categories: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'],
                crosshair: true,
                labels: {
                    style: {
                        fontSize: '12px'
                    }
                }
            },
            yAxis: {
                min: 0,
                title: {
                    text: 'Water Withdrawal (m³)',
                    style: {
                        fontSize: '12px'
                    }
                },
                labels: {
                    style: {
                        fontSize: '11px'
                    }
                }
            },
            tooltip: {
                headerFormat: '<span style="font-size:10px">{point.key}</span><table>',
                pointFormat: '<tr><td style="color:{series.color};padding:0">{series.name}: </td>' +
                    '<td style="padding:0"><b>{point.y:,.0f} m³</b></td></tr>',
                footerFormat: '</table>',
                shared: true,
                useHTML: true
            },
            plotOptions: {
                column: {
                    pointPadding: 0.2,
                    borderWidth: 0,
                    groupPadding: 0.1
                }
            },
            legend: {
                enabled: selectedWaterEntities.length > 1,
                align: 'center',
                verticalAlign: 'bottom',
                layout: 'horizontal',
                itemStyle: {
                    fontSize: '12px'
                }
            },
            series: generateWaterWithdrawalData()
        };

        setPieChartOptions(pieOptions);
        setEnergyBarChartOptions(energyBarOptions);
        setWaterBarChartOptions(waterBarOptions);
    }, [selectedYear, selectedCountry, selectedPeriod, energyWithdrawalYear, waterWithdrawalCountry, selectedWaterEntities, startDate, endDate]);

    const generateReport = () => {
        console.log('Generating report with filters:', {
            year: selectedYear,
            country: selectedCountry,
            period: selectedPeriod,
            framework: selectedFramework,
            category: selectedCategory
        });
    };

    const downloadReport = () => {
        console.log('Downloading report...');
    };

    // Handle no access case
    if (userRole === "none") {
        return (
            <div style={{ padding: "24px", fontFamily: "Arial, sans-serif" }}>
                <h3>You are not configured as an Admin.</h3>
                <p>Please contact your Corporate Admin to get access.</p>
            </div>
        );
    }

    return (
        <div style={{ fontFamily: "Arial, sans-serif", fontSize: "14px" }}>
            <div
                style={{
                    backgroundColor: "#f9f9f9",
                    padding: "16px 24px",
                    display: "flex",
                    justifyContent: "space-between",
                }}
            >
                <div>
                    <div style={{ fontSize: "16px", fontWeight: "bold" }}>Reports & Disclosure</div>
                    <div style={{ fontSize: "13px", color: "#555" }}>
                        {userRole === "corporate"
                            ? "Corporate Admin: Viewing all countries"
                            : `Viewing reports for: ${userCountries.map((c) => c.name).join(", ")}`}
                    </div>
                </div>
            </div>

            <div style={{ display: "flex", height: "calc(100vh - 70px)" }}>
                {/* Left Pane - Reportclosure Navigation */}
                <div
                    style={{
                        width: "300px",
                        background: "#f9f9f9",
                        borderRight: "1px solid #ddd",
                        padding: "8px",
                        overflowY: "auto",
                    }}
                >
                    {countryList.map(([country, types]) => (
                        <div key={country} style={{ border: "1px solid #ccc", marginBottom: "0px" }}>
                            <div
                                onClick={() => toggleCountry(country)}
                                style={{
                                    padding: "10px 12px",
                                    backgroundColor: "#e6e6e6",
                                    fontWeight: "bold",
                                    color: "#333",
                                    display: "flex",
                                    justifyContent: "space-between",
                                    cursor: "pointer",
                                }}
                            >
                                <span>{country.toUpperCase()}</span>
                                <Chevron expanded={expandedCountry[country]} direction="right" />
                            </div>

                            {expandedCountry[country] &&
                                Object.entries(types).map(([type, reports]) => {
                                    const typeKey = `${country}-${type}`;
                                    return (
                                        <div key={type}>
                                            <div
                                                onClick={() => toggleType(country, type)}
                                                style={{
                                                    padding: "8px 16px",
                                                    backgroundColor: "#f5f5f5",
                                                    fontWeight: "600",
                                                    display: "flex",
                                                    alignItems: "center",
                                                    cursor: "pointer",
                                                }}
                                            >
                                                <Chevron expanded={expandedType[typeKey]} direction="left" />
                                                {type}
                                            </div>
                                            {expandedType[typeKey] && (
                                                <ul style={{ listStyle: "none", paddingLeft: "64px", margin: "4px 0" }}>
                                                    {reports.map((report, i) => {
                                                        const isSelected =
                                                            selectedReport.country === country &&
                                                            selectedReport.name === report;
                                                        return (
                                                            <li
                                                                key={i}
                                                                onClick={() => setSelectedReport({ country, name: report })}
                                                                style={{
                                                                    padding: "6px 8px",
                                                                    cursor: "pointer",
                                                                    backgroundColor: isSelected ? "#e0ecff" : "transparent",
                                                                    color: isSelected ? "#0d47a1" : "#333",
                                                                    borderRight: isSelected ? "3px solid #0d47a1" : "none",
                                                                }}
                                                            >
                                                                {report}
                                                            </li>
                                                        );
                                                    })}
                                                </ul>
                                            )}
                                        </div>
                                    );
                                })}
                        </div>
                    ))}
                </div>

                {/* Right Pane - ESG Reports Content */}
                <div style={{ flex: 1, padding: "0", overflowY: "auto" }}>
                    {selectedReport.name === "ESG" ? (
                        <div className="grid bg-smoke" style={{ minHeight: '100vh', padding: '20px' }}>
                            <div className="col-12">
                                <div className="card p-4">
                                    {/* Header */}
                                    <div className="flex justify-content-between align-items-center mb-4">
                                        <div>
                                            <h2 className="text-bold fs-24 mb-1">ESG Report</h2>
                                            <span className="clr-gray-3 fs-14">{selectedReport.country} • Internal Framework • ESG</span>
                                        </div>
                                        <div className="text-right">
                                            <div className="text-bold fs-18">Jun 11, 2025</div>
                                            <div className="clr-gray-3 fs-12">Last Updated</div>
                                        </div>
                                    </div>

                                    {/* Report Configuration */}
                                    <div className="mb-4">
                                        <h3 className="text-bold fs-18 mb-3">Report Configuration</h3>
                                        <p className="clr-gray-3 fs-14 mb-3">Configure your filters and generate comprehensive ESG reports</p>

                                        <div className="grid">
                                            <div className="col-2">
                                                <label className="text-bold fs-12 mb-2 block">Year</label>
                                                <Dropdown
                                                    value={selectedYear}
                                                    options={yearOptions}
                                                    onChange={(e) => setSelectedYear(e.value)}
                                                    className="w-full"
                                                />
                                            </div>
                                            <div className="col-2">
                                                <label className="text-bold fs-12 mb-2 block">Country</label>
                                                <Dropdown
                                                    value={selectedCountry}
                                                    options={countryOptions}
                                                    onChange={(e) => setSelectedCountry(e.value)}
                                                    className="w-full"
                                                />
                                            </div>
                                            <div className="col-4">
                                                <label className="text-bold fs-12 mb-2 block">Reporting Period</label>
                                                <div className="flex align-items-center gap-2">
                                                    <Dropdown
                                                        value={selectedPeriod}
                                                        options={periodOptions}
                                                        onChange={(e) => {
                                                            setSelectedPeriod(e.value);
                                                            if (e.value !== 'Custom') {
                                                                setStartDate(null);
                                                                setEndDate(null);
                                                            }
                                                        }}
                                                        className="w-full"
                                                    />
                                                    {selectedPeriod === 'Custom' && (
                                                        <div className="flex gap-2">
                                                            <Calendar
                                                                value={startDate}
                                                                onChange={(e) => {
                                                                    setStartDate(e.value);
                                                                    if (endDate && e.value && endDate < e.value) {
                                                                        setEndDate(null);
                                                                    }
                                                                }}
                                                                dateFormat="mm/dd/yy"
                                                                className="w-full"
                                                                placeholder="Start date"
                                                                showIcon
                                                                style={{ fontSize: '10px' }}
                                                                maxDate={new Date()}
                                                            />
                                                            <Calendar
                                                                value={endDate}
                                                                onChange={(e) => setEndDate(e.value)}
                                                                dateFormat="mm/dd/yy"
                                                                className="w-full"
                                                                placeholder="End date"
                                                                showIcon
                                                                minDate={startDate}
                                                                maxDate={new Date()}
                                                                style={{ fontSize: '10px' }}
                                                                disabled={!startDate}
                                                            />
                                                        </div>
                                                    )}
                                                </div>
                                                {selectedPeriod === 'Custom' && startDate && endDate && (
                                                    <div className="mt-2 p-2 bg-green-50 br-5">
                                                        <p className="fs-10 clr-green-600 text-center">
                                                            {Math.ceil((endDate - startDate) / (1000 * 60 * 60 * 24 * 30))} month(s) selected
                                                        </p>
                                                    </div>
                                                )}
                                            </div>
                                            <div className="col-4">
                                                <label className="text-bold fs-12 mb-2 block">&nbsp;</label>
                                                <div className="flex justify-content-end gap-2">
                                                    <Button
                                                        label="Generate Report"
                                                        className="p-button-primary"
                                                        onClick={generateReport}
                                                    />
                                                    <Button
                                                        icon="pi pi-download"
                                                        className="p-button-outlined"
                                                        onClick={downloadReport}
                                                        tooltip="Download Report"
                                                    />
                                                    <Button
                                                        label="History"
                                                        className="p-button-outlined"
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Main Metrics Cards */}
                                    <div className="grid">
                                        <div className="col-12">
                                            <div className="card bg-white p-4 mb-3">
                                                <h4 className="text-bold fs-16 mb-2">Total Customer IT Load for {selectedCountry} for the {selectedYear}</h4>
                                                <div className="flex justify-content-between align-items-center">
                                                    <div>
                                                        <div className="text-bold fs-36 clr-navy">{calculateDynamicData().itLoad.toLocaleString()} MWh</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="col-12">
                                            <div className="card bg-white p-4 mb-3">
                                                <h4 className="text-bold fs-16 mb-2">Total Fuel Consumption for {selectedCountry} for the {selectedYear}</h4>
                                                <div className="flex justify-content-between align-items-center">
                                                    <div>
                                                        <div className="text-bold fs-36 clr-delete">{calculateDynamicData().fuelConsumption.toLocaleString()} L</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div className="col-12">
                                            <div className="card bg-white p-4 mb-3">
                                                <h4 className="text-bold fs-16 mb-2">Total Water Consumption for {selectedCountry} for the {selectedYear} ({getBreakdownType()})</h4>
                                                <div className="flex justify-content-between align-items-center mb-4">
                                                    <div>
                                                        <div className="text-bold fs-36 clr-navy">{calculateDynamicData().waterConsumption.toLocaleString()} m³</div>
                                                    </div>
                                                </div>

                                                {/* Breakdown Section */}
                                                <div className="mt-4">
                                                    <h5 className="text-bold fs-14 mb-3">
                                                        {getBreakdownType()}
                                                        {selectedPeriod === 'Custom' && startDate && endDate && (
                                                            <span className="clr-gray-3 fs-12 ml-2">
                                                                ({startDate.toLocaleDateString()} - {endDate.toLocaleDateString()})
                                                            </span>
                                                        )}
                                                    </h5>
                                                    <div className="grid">
                                                        {calculateDynamicData().monthlyData.map((month, index) => {
                                                            const dataLength = calculateDynamicData().monthlyData.length;
                                                            let colSize = 'col-1';

                                                            // Determine column size based on period type and data length
                                                            if (selectedPeriod === 'Yearly') {
                                                                colSize = 'col-12'; // Full width for single year column
                                                            } else if (selectedPeriod === 'Quarterly') {
                                                                colSize = 'col-4'; // 3 months = 3 columns (col-4 each)
                                                            } else if (selectedPeriod === 'Half yearly') {
                                                                colSize = 'col-2'; // 6 months = 6 columns
                                                            } else if (selectedPeriod === 'Monthly') {
                                                                colSize = 'col-1'; // 12 months = 12 columns
                                                            } else if (dataLength <= 3) {
                                                                colSize = 'col-4';
                                                            } else if (dataLength <= 6) {
                                                                colSize = 'col-2';
                                                            } else {
                                                                colSize = 'col-1';
                                                            }

                                                            return (
                                                                <div key={index} className={`${colSize} text-center mb-2`}>
                                                                    <div className="p-3 bg-gray-50 br-5">
                                                                        <div className="clr-gray-3 fs-12 mb-1">
                                                                            {selectedPeriod === 'Yearly' ? selectedYear : month.month.toUpperCase()}
                                                                        </div>
                                                                        <div className="text-bold fs-20">{month.value.toLocaleString()}</div>
                                                                    </div>
                                                                </div>
                                                            );
                                                        })}
                                                    </div>

                                                    {/* Summary for Monthly/Custom periods */}
                                                    {(selectedPeriod === 'Monthly' || selectedPeriod === 'Custom') && (
                                                        <div className="mt-3 p-3 bg-blue-50 br-5">
                                                            <div className="grid">
                                                                <div className="col-4 text-center">
                                                                    <div className="text-bold fs-16 clr-navy">
                                                                        {calculateDynamicData().monthlyData.reduce((sum, month) => sum + month.value, 0).toLocaleString()}
                                                                    </div>
                                                                    <div className="clr-gray-3 fs-12">Total</div>
                                                                </div>
                                                                <div className="col-4 text-center">
                                                                    <div className="text-bold fs-16 clr-navy">
                                                                        {Math.round(calculateDynamicData().monthlyData.reduce((sum, month) => sum + month.value, 0) / calculateDynamicData().monthlyData.length).toLocaleString()}
                                                                    </div>
                                                                    <div className="clr-gray-3 fs-12">Average</div>
                                                                </div>
                                                                <div className="col-4 text-center">
                                                                    <div className="text-bold fs-16 clr-navy">
                                                                        {calculateDynamicData().monthlyData.length}
                                                                    </div>
                                                                    <div className="clr-gray-3 fs-12">
                                                                        {selectedPeriod === 'Monthly' ? 'Months' : 'Periods'}
                                                                    </div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Renewable Energy Consumption Section */}
                                        <div className="col-6">
                                            <div className="card bg-white p-4 mb-3">
                                                <h4 className="text-bold fs-16 mb-3">Renewable Energy Consumption for {selectedCountry} for the {selectedYear}</h4>
                                                {/* Energy Spend Breakdown */}
                                                <div className="mb-4">
                                                    <h5 className="text-bold fs-14 mb-3">Energy Spend Breakdown</h5>
                                                    <div className="chart-container">
                                                        <HighchartsReact
                                                            highcharts={Highcharts}
                                                            options={pieChartOptions}
                                                        />
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Carbon Breakdown Section */}
                                        <div className="col-6">
                                            <div className="card bg-white p-4 mb-3">
                                                <h4 className="text-bold fs-16 mb-3">Carbon Breakdown</h4>
                                                <div className="mb-3">
                                                    {carbonBreakdownData.map((item, index) => (
                                                        <div key={index} className="flex justify-content-between align-items-center mb-2">
                                                            <div className="flex align-items-center">
                                                                <div
                                                                    className="w-1rem h-1rem br-50 mr-2"
                                                                    style={{ backgroundColor: item.color }}
                                                                ></div>
                                                                <span className="fs-14">{item.source}</span>
                                                            </div>
                                                            <span className="text-bold fs-14">{item.percentage}%</span>
                                                        </div>
                                                    ))}
                                                </div>
                                            </div>
                                        </div>

                                        {/* Total Energy Withdrawal Section */}
                                        <div className="col-12">
                                            <div className="card bg-white p-4 mb-3">
                                                <div className="section-header">
                                                    <h4 className="text-bold fs-16">Total Energy Withdrawal for {selectedCountry}</h4>
                                                    <div className="flex align-items-center gap-2">
                                                        <label className="text-bold fs-12">Years:</label>
                                                        <MultiSelect
                                                            value={selectedEnergyYears}
                                                            options={yearOptions}
                                                            onChange={(e) => setSelectedEnergyYears(e.value)}
                                                            placeholder="Select Years"
                                                            className="filter-dropdown"
                                                            style={{ minWidth: '200px' }}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="grid">
                                                    <div className="col-8">
                                                        <div className="chart-container">
                                                            <HighchartsReact
                                                                highcharts={Highcharts}
                                                                options={energyBarChartOptions}
                                                            />
                                                        </div>
                                                    </div>
                                                    <div className="col-4">
                                                        <div className="text-center">
                                                            <h5 className="text-bold fs-14 mb-2">Summary Statistics</h5>
                                                            <div className="mb-3">
                                                                <div className="text-bold fs-24 clr-navy">11,090</div>
                                                                <div className="clr-gray-3 fs-12">Average Monthly Withdrawal</div>
                                                            </div>
                                                            <div className="mb-3">
                                                                <div className="text-bold fs-24 clr-navy">11,090</div>
                                                                <div className="clr-gray-3 fs-12">Peak Monthly Withdrawal</div>
                                                            </div>
                                                            <div className="mb-3">
                                                                <div className="text-bold fs-24 clr-navy">11,090</div>
                                                                <div className="clr-gray-3 fs-12">Lowest Monthly Withdrawal</div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Renewable Energy Consumption Percentage */}
                                        <div className="col-12">
                                            <div className="card bg-white p-4 mb-3">
                                                <h4 className="text-bold fs-16 mb-3">Renewable Energy Consumption Percentage for {selectedCountry} for the {selectedYear}</h4>
                                                <div className="flex justify-content-between align-items-center">
                                                    <div>
                                                        <div className="text-bold fs-36 text-green-600">{calculateDynamicData().renewablePercentage.toFixed(1)}%</div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Total Water Withdrawal Section */}
                                        <div className="col-12">
                                            <div className="card bg-white p-4 mb-4">
                                                <div className="section-header mb-4">
                                                    <h4 className="text-bold fs-16">Total Water Withdrawal</h4>
                                                    <div className="flex align-items-center gap-2">
                                                        <label className="text-bold fs-12">Entity:</label>
                                                        <MultiSelect
                                                            value={selectedWaterEntities}
                                                            options={countryOptions}
                                                            onChange={(e) => setSelectedWaterEntities(e.value)}
                                                            placeholder="Select Entities"
                                                            className="filter-dropdown"
                                                            style={{ minWidth: '200px' }}
                                                        />
                                                    </div>
                                                </div>
                                                <div className="chart-container" style={{ height: '400px', marginBottom: '1rem' }}>
                                                    <HighchartsReact
                                                        highcharts={Highcharts}
                                                        options={waterBarChartOptions}
                                                    />
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div style={{ padding: "24px" }}>
                            <h2 style={{ fontSize: "20px", fontWeight: "bold", marginBottom: "16px" }}>
                                {selectedReport.name ? `${selectedReport.name} Report` : "Select a Report"}
                            </h2>
                            <p style={{ color: "#666" }}>
                                {selectedReport.name
                                    ? `Report for ${selectedReport.country} - ${selectedReport.name}`
                                    : "Choose a report from the left panel."}
                            </p>
                        </div>
                    )}
                </div>
            </div>
        </div>
    );
};

export default ESGReports;
